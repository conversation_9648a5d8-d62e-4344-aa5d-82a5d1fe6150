import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Textarea
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconCheck,
  IconCurrencyDollar,
  IconDownload,
  IconEdit,
  IconEye,
  IconFileText,
  IconPlus,
  IconPrinter,
  IconSearch,
  IconSend,
  IconTrash
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface SimpleQuote {
  id: string
  quoteNumber: string
  customerName: string
  customerEmail: string
  vehicleName: string
  vehicleCategory: string
  status: 'draft' | 'sent' | 'viewed' | 'accepted' | 'rejected' | 'expired'
  validUntil: string
  pickupDate: string
  returnDate: string
  totalDays: number
  dailyRate: number
  totalAmount: number
  createdDate: string
  notes: string
}

export function QuotesSimple() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)

  // Simple mock data
  const quotes: SimpleQuote[] = [
    {
      id: '1',
      quoteNumber: 'QUO-2024-001',
      customerName: 'Ahmed Al-Rashid',
      customerEmail: '<EMAIL>',
      vehicleName: 'Toyota Camry 2023',
      vehicleCategory: 'Economy',
      status: 'sent',
      validUntil: '2024-01-25',
      pickupDate: '2024-01-20',
      returnDate: '2024-01-25',
      totalDays: 5,
      dailyRate: 120,
      totalAmount: 600,
      createdDate: '2024-01-15',
      notes: 'Customer requested GPS and child seat'
    },
    {
      id: '2',
      quoteNumber: 'QUO-2024-002',
      customerName: 'Sarah Johnson',
      customerEmail: '<EMAIL>',
      vehicleName: 'BMW X5 2022',
      vehicleCategory: 'Luxury',
      status: 'accepted',
      validUntil: '2024-01-22',
      pickupDate: '2024-01-18',
      returnDate: '2024-01-22',
      totalDays: 4,
      dailyRate: 200,
      totalAmount: 800,
      createdDate: '2024-01-10',
      notes: 'Business customer, corporate rate applied'
    },
    {
      id: '3',
      quoteNumber: 'QUO-2024-003',
      customerName: 'Mohammed Hassan',
      customerEmail: '<EMAIL>',
      vehicleName: 'Mercedes C-Class 2023',
      vehicleCategory: 'Premium',
      status: 'viewed',
      validUntil: '2024-01-30',
      pickupDate: '2024-01-25',
      returnDate: '2024-01-30',
      totalDays: 5,
      dailyRate: 180,
      totalAmount: 900,
      createdDate: '2024-01-16',
      notes: 'First time customer, special discount applied'
    }
  ]

  const stats = [
    { label: t('totalQuotes'), value: '89', color: 'blue', icon: IconFileText },
    { label: t('pendingQuotes'), value: '12', color: 'orange', icon: IconCar},
    { label: t('acceptedQuotes'), value: '34', color: 'green', icon: IconCheck },
    { label: t('quoteValue'), value: '$67,890', color: 'red', icon: IconCurrencyDollar }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted': return 'green'
      case 'sent': return 'blue'
      case 'viewed': return 'cyan'
      case 'draft': return 'gray'
      case 'rejected': return 'red'
      case 'expired': return 'dark'
      default: return 'gray'
    }
  }

  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch = quote.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         quote.quoteNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         quote.vehicleName.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || quote.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('quotesManagement')}
        description={t('generateAndManageRentalQuotes')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconDownload size={16} />}>
              {t('exportQuotes')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
              {t('createQuote')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="quotes">{t('allQuotes')}</Tabs.Tab>
          <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Pending Actions */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('pendingActions')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('expiringSoon')}</Text>
                    <Text size="xs">3 {t('quotesExpiringIn24Hours')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconSend size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('awaitingResponse')}</Text>
                    <Text size="xs">8 {t('quotesAwaitingCustomerResponse')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconCheck size={16} />} color="green">
                    <Text fw={500} size="sm">{t('readyToConvert')}</Text>
                    <Text size="xs">5 {t('acceptedQuotesReadyForReservation')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Quotes */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('recentQuotes')}</Title>
                
                <Stack gap="sm">
                  {quotes.slice(0, 3).map((quote) => (
                    <Paper key={quote.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <div>
                          <Text fw={500} size="sm">{quote.customerName}</Text>
                          <Text size="xs" c="dimmed">{quote.vehicleName} - {quote.totalDays} days</Text>
                        </div>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getStatusColor(quote.status)} size="sm">
                            {t(quote.status)}
                          </Badge>
                          <Text size="xs" c="dimmed">${quote.totalAmount}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="quotes" pt="md">
          {/* Filters */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchQuotes')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'draft', label: t('draft') },
                    { value: 'sent', label: t('sent') },
                    { value: 'viewed', label: t('viewed') },
                    { value: 'accepted', label: t('accepted') },
                    { value: 'rejected', label: t('rejected') },
                    { value: 'expired', label: t('expired') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Quotes Table */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('quote')}</Table.Th>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('dates')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('validUntil')}</Table.Th>
                  <Table.Th>{t('total')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredQuotes.map((quote) => (
                  <Table.Tr key={quote.id}>
                    <Table.Td>
                      <Text fw={700} size="sm">{quote.quoteNumber}</Text>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{quote.customerName}</Text>
                        <Text size="xs" c="dimmed">{quote.customerEmail}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{quote.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{quote.vehicleCategory}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{quote.pickupDate} - {quote.returnDate}</Text>
                        <Text size="xs" c="dimmed">{quote.totalDays} {t('days')}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(quote.status)}>
                        {t(quote.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{quote.validUntil}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={700}>${quote.totalAmount}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconSend size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconPrinter size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm" color="red">
                          <IconTrash size={14} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="analytics" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">{t('quoteAnalytics')}</Title>
            <Text c="dimmed">{t('analyticsWillBeImplementedHere')}</Text>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Create Quote Modal */}
      <Modal
        opened={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        title={t('createNewQuote')}
        size="lg"
      >
        <Stack>
          <Select
            label={t('customer')}
            placeholder={t('selectCustomer')}
            data={[
              { value: '1', label: 'Ahmed Al-Rashid (<EMAIL>)' },
              { value: '2', label: 'Sarah Johnson (<EMAIL>)' },
              { value: '3', label: 'Mohammed Hassan (<EMAIL>)' }
            ]}
            required
          />

          <Grid>
            <Grid.Col span={6}>
              <Select
                label={t('vehicleCategory')}
                placeholder={t('selectCategory')}
                data={[
                  { value: 'economy', label: t('economy') },
                  { value: 'compact', label: t('compact') },
                  { value: 'premium', label: t('premium') },
                  { value: 'luxury', label: t('luxury') },
                  { value: 'suv', label: t('suv') }
                ]}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('rentalDays')}
                placeholder={t('enterDays')}
                required
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label={t('dailyRate')}
                placeholder={t('enterRate')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('totalAmount')}
                placeholder={t('calculated')}
                disabled
              />
            </Grid.Col>
          </Grid>

          <Textarea
            label={t('notes')}
            placeholder={t('enterQuoteNotes')}
            rows={3}
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setAddModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button variant="light" leftSection={<IconFileText size={16} />}>
              {t('saveDraft')}
            </Button>
            <Button leftSection={<IconSend size={16} />} onClick={() => setAddModalOpen(false)}>
              {t('createAndSend')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}

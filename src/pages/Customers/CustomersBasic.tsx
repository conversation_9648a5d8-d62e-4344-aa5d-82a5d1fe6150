import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar,
  Progress
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCar,
  IconDownload,
  IconEdit,
  IconEye,
  IconMail,
  IconPhone,
  IconPlus,
  IconSearch,
  IconStar,
  IconUser,
  IconUserPlus
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { PageHeader } from '../../components/Common/PageHeader'

interface BasicCustomer {
  id: string
  customerNumber: string
  name: string
  email: string
  phone: string
  country: string
  status: 'active' | 'inactive' | 'vip' | 'blocked'
  totalRentals: number
  totalSpent: number
  lastRental: string
  joinDate: string
  rating: number
}

export function CustomersBasic() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [addModalOpen, setAddModalOpen] = useState(false)

  // Simple mock data
  const customers: BasicCustomer[] = [
    {
      id: '1',
      customerNumber: 'CUS-2024-001',
      name: 'Ahmed Al-Rashid',
      email: '<EMAIL>',
      phone: '+971-50-123-4567',
      country: 'UAE',
      status: 'vip',
      totalRentals: 15,
      totalSpent: 12500,
      lastRental: '2024-01-15',
      joinDate: '2023-03-10',
      rating: 5
    },
    {
      id: '2',
      customerNumber: 'CUS-2024-002',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '******-987-6543',
      country: 'USA',
      status: 'active',
      totalRentals: 8,
      totalSpent: 6800,
      lastRental: '2024-01-10',
      joinDate: '2023-07-22',
      rating: 4
    },
    {
      id: '3',
      customerNumber: 'CUS-2024-003',
      name: 'Mohammed Hassan',
      email: '<EMAIL>',
      phone: '+971-55-789-0123',
      country: 'UAE',
      status: 'active',
      totalRentals: 3,
      totalSpent: 2100,
      lastRental: '2024-01-05',
      joinDate: '2023-11-15',
      rating: 4
    },
    {
      id: '4',
      customerNumber: 'CUS-2024-004',
      name: 'Emma Wilson',
      email: '<EMAIL>',
      phone: '+44-20-1234-5678',
      country: 'UK',
      status: 'inactive',
      totalRentals: 1,
      totalSpent: 450,
      lastRental: '2023-12-01',
      joinDate: '2023-11-28',
      rating: 3
    }
  ]

  const stats = [
    { label: t('totalCustomers'), value: '1,247', color: 'blue', icon: IconUser },
    { label: t('activeCustomers'), value: '892', color: 'green', icon: IconUserPlus },
    { label: t('vipCustomers'), value: '45', color: 'yellow', icon: IconStar },
    { label: t('newThisMonth'), value: '67', color: 'red', icon: IconCar}
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green'
      case 'vip': return 'yellow'
      case 'inactive': return 'gray'
      case 'blocked': return 'red'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         customer.customerNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || customer.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Stack gap="lg">
      <PageHeader
        title={t('customersManagement')}
        description={t('manageCustomerDatabaseAndRelationships')}
        actions={
          <Group>
            <Button variant="light" leftSection={<IconDownload size={16} />}>
              {t('exportCustomers')}
            </Button>
            <Button leftSection={<IconPlus size={16} />} onClick={() => setAddModalOpen(true)}>
              {t('addCustomer')}
            </Button>
          </Group>
        }
      />

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="customers">{t('allCustomers')}</Tabs.Tab>
          <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Customer Alerts */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('customerAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('inactiveCustomers')}</Text>
                    <Text size="xs">23 {t('customersInactiveFor90Days')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconStar size={16} />} color="yellow">
                    <Text fw={500} size="sm">{t('vipOpportunities')}</Text>
                    <Text size="xs">12 {t('customersEligibleForVipStatus')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconMail size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('followUpRequired')}</Text>
                    <Text size="xs">8 {t('customersRequireFollowUp')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Customer Segments */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('customerSegments')}</Title>
                
                <Stack gap="sm">
                  <Group justify="space-between">
                    <Group gap="sm">
                      <IconStar size={16} color="var(--mantine-color-yellow-6)" />
                      <Text size="sm">{t('vipCustomers')}</Text>
                    </Group>
                    <Text fw={700}>3.6%</Text>
                  </Group>
                  <Progress value={3.6} color="yellow" size="sm" />

                  <Group justify="space-between">
                    <Group gap="sm">
                      <IconUser size={16} color="var(--mantine-color-green-6)" />
                      <Text size="sm">{t('activeCustomers')}</Text>
                    </Group>
                    <Text fw={700}>71.5%</Text>
                  </Group>
                  <Progress value={71.5} color="green" size="sm" />

                  <Group justify="space-between">
                    <Group gap="sm">
                      <IconUser size={16} color="var(--mantine-color-gray-6)" />
                      <Text size="sm">{t('inactiveCustomers')}</Text>
                    </Group>
                    <Text fw={700}>24.9%</Text>
                  </Group>
                  <Progress value={24.9} color="gray" size="sm" />
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>

          {/* Top Customers */}
          <Card withBorder mt="lg">
            <Title order={4} mb="md">{t('topCustomers')}</Title>
            
            <Stack gap="sm">
              {customers.slice(0, 3).map((customer) => (
                <Paper key={customer.id} p="sm" withBorder>
                  <Group justify="space-between">
                    <Group>
                      <Avatar color={getStatusColor(customer.status)} radius="xl">
                        {getInitials(customer.name)}
                      </Avatar>
                      <div>
                        <Text fw={500} size="sm">{customer.name}</Text>
                        <Text size="xs" c="dimmed">{customer.email}</Text>
                      </div>
                    </Group>
                    <div style={{ textAlign: 'right' }}>
                      <Text fw={700}>${customer.totalSpent.toLocaleString()}</Text>
                      <Text size="xs" c="dimmed">{customer.totalRentals} {t('rentals')}</Text>
                    </div>
                  </Group>
                </Paper>
              ))}
            </Stack>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="customers" pt="md">
          {/* Filters */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchCustomers')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'active', label: t('active') },
                    { value: 'vip', label: t('vip') },
                    { value: 'inactive', label: t('inactive') },
                    { value: 'blocked', label: t('blocked') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Customers Table */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('contact')}</Table.Th>
                  <Table.Th>{t('country')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('rentals')}</Table.Th>
                  <Table.Th>{t('totalSpent')}</Table.Th>
                  <Table.Th>{t('lastRental')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredCustomers.map((customer) => (
                  <Table.Tr key={customer.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getStatusColor(customer.status)} radius="xl" size="sm">
                          {getInitials(customer.name)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{customer.name}</Text>
                          <Text size="xs" c="dimmed">{customer.customerNumber}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{customer.email}</Text>
                        <Text size="xs" c="dimmed">{customer.phone}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{customer.country}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(customer.status)}>
                        {t(customer.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500}>{customer.totalRentals}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={700}>${customer.totalSpent.toLocaleString()}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm">{customer.lastRental}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconPhone size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconMail size={14} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="analytics" pt="md">
          <Card withBorder>
            <Title order={4} mb="md">{t('customerAnalytics')}</Title>
            <Text c="dimmed">{t('analyticsWillBeImplementedHere')}</Text>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Add Customer Modal */}
      <Modal
        opened={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        title={t('addNewCustomer')}
        size="lg"
      >
        <Stack>
          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label={t('firstName')}
                placeholder={t('enterFirstName')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('lastName')}
                placeholder={t('enterLastName')}
                required
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label={t('email')}
                placeholder={t('enterEmail')}
                type="email"
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('phone')}
                placeholder={t('enterPhone')}
                required
              />
            </Grid.Col>
          </Grid>

          <Select
            label={t('country')}
            placeholder={t('selectCountry')}
            data={[
              { value: 'UAE', label: 'United Arab Emirates' },
              { value: 'USA', label: 'United States' },
              { value: 'UK', label: 'United Kingdom' },
              { value: 'SA', label: 'Saudi Arabia' }
            ]}
            required
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setAddModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconUserPlus size={16} />} onClick={() => setAddModalOpen(false)}>
              {t('addCustomer')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  )
}

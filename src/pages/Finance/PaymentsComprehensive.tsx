import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar,
  NumberInput,
  Textarea,
  Pagination,
  ThemeIcon,
  Progress,
  Checkbox
} from '@mantine/core'
import { DateInput } from '@mantine/dates'
import {
  IconAlertTriangle,
  IconCheck,
  IconClock,
  IconCreditCard,
  IconDownload,
  IconEye,
  IconPlus,
  IconSearch,
  IconFilter,
  IconRefresh,
  IconCurrencyDollar,
  IconSettings,
  IconTrendingUp,
  IconTrendingDown,
  IconX,
  IconBan,
  IconReceipt,
  IconPrinter,
  IconMail,
  IconCreditCardOff,
  IconStack,
  IconFileExport
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface PaymentData {
  id: string
  payment_id: string
  customer_name: string
  customer_email: string
  customer_phone: string
  contract_number: string
  vehicle_info: string
  payment_method: 'credit-card' | 'debit-card' | 'cash' | 'bank-transfer' | 'digital-wallet' | 'cheque'
  payment_type: 'rental-fee' | 'deposit' | 'security-deposit' | 'late-fee' | 'damage-fee' | 'fuel-charge' | 'cleaning-fee' | 'toll-fee' | 'insurance-fee' | 'cancellation-fee'
  amount: number
  currency: string
  payment_date: string
  due_date: string
  status: 'completed' | 'pending' | 'processing' | 'failed' | 'refunded' | 'disputed' | 'cancelled' | 'overdue'
  transaction_id: string
  reference_number: string
  location: string
  processed_by: string
  gateway: string
  fees: number
  net_amount: number
  notes: string
  receipt_sent: boolean
  refund_amount?: number
  refund_date?: string
  refund_reason?: string
  dispute_reason?: string
  created_at: string
  updated_at: string
}

export function PaymentsComprehensive() {
  const { t } = useTranslation()
  
  // State management
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [methodFilter, setMethodFilter] = useState<string>('')
  const [typeFilter, setTypeFilter] = useState<string>('')

  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  
  // Modal states
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [createModalOpen, setCreateModalOpen] = useState(false)
  const [refundModalOpen, setRefundModalOpen] = useState(false)
  const [batchModalOpen, setBatchModalOpen] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<PaymentData | null>(null)
  const [selectedPayments, setSelectedPayments] = useState<string[]>([])
  const [batchOperation, setBatchOperation] = useState<string>('')

  // Comprehensive mock data
  const payments: PaymentData[] = [
    {
      id: '1',
      payment_id: 'PAY-2024-001',
      customer_name: 'Ahmed Al-Rashid',
      customer_email: '<EMAIL>',
      customer_phone: '+971-50-123-4567',
      contract_number: 'CON-2024-001',
      vehicle_info: 'BMW X5 - ABC-1234',
      payment_method: 'credit-card',
      payment_type: 'rental-fee',
      amount: 1200,
      currency: 'AED',
      payment_date: '2024-01-15',
      due_date: '2024-01-15',
      status: 'completed',
      transaction_id: 'TXN-789123456',
      reference_number: 'REF-001-2024',
      location: 'Dubai Airport',
      processed_by: 'Sarah Ahmed',
      gateway: 'Stripe',
      fees: 36,
      net_amount: 1164,
      notes: 'Payment for 3-day luxury car rental',
      receipt_sent: true,
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T10:35:00Z'
    },
    {
      id: '2',
      payment_id: 'PAY-2024-002',
      customer_name: 'Sarah Johnson',
      customer_email: '<EMAIL>',
      customer_phone: '+971-55-987-6543',
      contract_number: 'CON-2024-002',
      vehicle_info: 'Mercedes E-Class - XYZ-5678',
      payment_method: 'digital-wallet',
      payment_type: 'security-deposit',
      amount: 2000,
      currency: 'AED',
      payment_date: '2024-01-20',
      due_date: '2024-01-20',
      status: 'pending',
      transaction_id: 'TXN-789123457',
      reference_number: 'REF-002-2024',
      location: 'Abu Dhabi Marina',
      processed_by: 'Mohammed Ali',
      gateway: 'PayPal',
      fees: 60,
      net_amount: 1940,
      notes: 'Security deposit for luxury vehicle',
      receipt_sent: false,
      created_at: '2024-01-20T14:15:00Z',
      updated_at: '2024-01-20T14:20:00Z'
    },
    {
      id: '3',
      payment_id: 'PAY-2024-003',
      customer_name: 'Mohammed Hassan',
      customer_email: '<EMAIL>',
      customer_phone: '+971-52-456-7890',
      contract_number: 'CON-2024-003',
      vehicle_info: 'Toyota Camry - DEF-9012',
      payment_method: 'bank-transfer',
      payment_type: 'late-fee',
      amount: 150,
      currency: 'AED',
      payment_date: '2024-01-18',
      due_date: '2024-01-17',
      status: 'failed',
      transaction_id: 'TXN-*********',
      reference_number: 'REF-003-2024',
      location: 'Sharjah City Center',
      processed_by: 'Fatima Al-Zahra',
      gateway: 'Bank Transfer',
      fees: 5,
      net_amount: 145,
      notes: 'Late return penalty fee',
      receipt_sent: false,
      created_at: '2024-01-18T16:45:00Z',
      updated_at: '2024-01-18T16:50:00Z'
    },
    {
      id: '4',
      payment_id: 'PAY-2024-004',
      customer_name: 'Emily Chen',
      customer_email: '<EMAIL>',
      customer_phone: '+971-56-789-0123',
      contract_number: 'CON-2024-004',
      vehicle_info: 'Range Rover Sport - GHI-3456',
      payment_method: 'credit-card',
      payment_type: 'damage-fee',
      amount: 800,
      currency: 'AED',
      payment_date: '2024-01-22',
      due_date: '2024-01-22',
      status: 'disputed',
      transaction_id: 'TXN-*********',
      reference_number: 'REF-004-2024',
      location: 'Dubai Marina',
      processed_by: 'Ahmed Khalil',
      gateway: 'Stripe',
      fees: 24,
      net_amount: 776,
      notes: 'Minor scratch repair cost',
      receipt_sent: true,
      dispute_reason: 'Customer disputes damage assessment',
      created_at: '2024-01-22T11:20:00Z',
      updated_at: '2024-01-22T15:30:00Z'
    },
    {
      id: '5',
      payment_id: 'PAY-2024-005',
      customer_name: 'David Wilson',
      customer_email: '<EMAIL>',
      customer_phone: '+971-50-234-5678',
      contract_number: 'CON-2024-005',
      vehicle_info: 'Audi A6 - JKL-7890',
      payment_method: 'cash',
      payment_type: 'rental-fee',
      amount: 900,
      currency: 'AED',
      payment_date: '2024-01-25',
      due_date: '2024-01-25',
      status: 'refunded',
      transaction_id: 'TXN-789123460',
      reference_number: 'REF-005-2024',
      location: 'Al Ain',
      processed_by: 'Layla Hassan',
      gateway: 'Cash',
      fees: 0,
      net_amount: 900,
      notes: 'Full refund due to vehicle breakdown',
      receipt_sent: true,
      refund_amount: 900,
      refund_date: '2024-01-26',
      refund_reason: 'Vehicle mechanical failure',
      created_at: '2024-01-25T09:15:00Z',
      updated_at: '2024-01-26T10:00:00Z'
    }
  ]

  // Statistics calculation
  const stats = [
    { 
      label: t('totalPayments'), 
      value: payments.length.toLocaleString(), 
      color: 'blue', 
      icon: IconCreditCard,
      change: '+12%',
      trend: 'up'
    },
    { 
      label: t('completed') + ' ' + t('payments'),
      value: payments.filter(p => p.status === 'completed').length.toString(), 
      color: 'green', 
      icon: IconCheck,
      change: '+8%',
      trend: 'up'
    },
    { 
      label: t('pendingPayments'), 
      value: payments.filter(p => p.status === 'pending').length.toString(), 
      color: 'orange', 
      icon: IconClock,
      change: '-5%',
      trend: 'down'
    },
    { 
      label: t('totalRevenue'), 
      value: `AED ${payments.filter(p => p.status === 'completed').reduce((sum, p) => sum + p.net_amount, 0).toLocaleString()}`, 
      color: 'green', 
      icon: IconCurrencyDollar,
      change: '+15%',
      trend: 'up'
    }
  ]

  // Utility functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green'
      case 'pending': return 'orange'
      case 'processing': return 'blue'
      case 'failed': return 'red'
      case 'refunded': return 'purple'
      case 'disputed': return 'yellow'
      case 'cancelled': return 'gray'
      case 'overdue': return 'red'
      default: return 'gray'
    }
  }

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'credit-card': return 'blue'
      case 'debit-card': return 'green'
      case 'cash': return 'yellow'
      case 'bank-transfer': return 'purple'
      case 'digital-wallet': return 'red'
      case 'cheque': return 'gray'
      default: return 'gray'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'rental-fee': return 'blue'
      case 'deposit': return 'green'
      case 'security-deposit': return 'cyan'
      case 'late-fee': return 'red'
      case 'damage-fee': return 'orange'
      case 'fuel-charge': return 'yellow'
      case 'cleaning-fee': return 'purple'
      case 'toll-fee': return 'pink'
      case 'insurance-fee': return 'indigo'
      case 'cancellation-fee': return 'gray'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  // Filtering logic
  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.payment_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.contract_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payment.transaction_id.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = !statusFilter || payment.status === statusFilter
    const matchesMethod = !methodFilter || payment.payment_method === methodFilter
    const matchesType = !typeFilter || payment.payment_type === typeFilter
    
    return matchesSearch && matchesStatus && matchesMethod && matchesType
  })

  // Pagination
  const totalPages = Math.ceil(filteredPayments.length / pageSize)
  const paginatedPayments = filteredPayments.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  )

  return (
    <Container size="xl" py="md">
      {/* Header */}
      <Group justify="space-between" align="center" mb="lg">
        <div>
          <Title order={2}>{t('payments')}</Title>
          <Text c="dimmed" size="sm">{t('paymentManagement')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportPayments')}
          </Button>
          <Button variant="light" leftSection={<IconRefresh size={16} />}>
            {t('syncPayments')}
          </Button>
          <Button variant="light" leftSection={<IconStack size={16} />} onClick={() => setBatchModalOpen(true)}>
            Create New Batch Operation
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setCreateModalOpen(true)}>
            {t('recordPayment')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="payments">{t('allPayments')}</Tabs.Tab>
          <Tabs.Tab value="analytics">{t('analytics')}</Tabs.Tab>
          <Tabs.Tab value="reports">{t('reports')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Statistics Cards */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between" mb="xs">
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                      {stat.label}
                    </Text>
                    <ThemeIcon color={stat.color} variant="light" size="sm">
                      <stat.icon size={16} />
                    </ThemeIcon>
                  </Group>
                  <Group justify="space-between" align="flex-end">
                    <Text fw={700} size="xl">
                      {stat.value}
                    </Text>
                    <Group gap={4}>
                      {stat.trend === 'up' ? (
                        <IconTrendingUp size={16} color="green" />
                      ) : (
                        <IconTrendingDown size={16} color="red" />
                      )}
                      <Text size="xs" c={stat.trend === 'up' ? 'green' : 'red'}>
                        {stat.change}
                      </Text>
                    </Group>
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Payment Alerts */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Group justify="space-between" mb="md">
                  <Title order={4}>{t('payment')} {t('alerts')}</Title>
                  <Badge color="red" variant="light">
                    {payments.filter(p => ['failed', 'overdue', 'disputed'].includes(p.status)).length}
                  </Badge>
                </Group>

                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('failed')} {t('payments')}</Text>
                    <Text size="xs">{payments.filter(p => p.status === 'failed').length} {t('payments')} {t('require')} {t('attention')}</Text>
                  </Alert>

                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('pending')} {t('payments')}</Text>
                    <Text size="xs">{payments.filter(p => p.status === 'pending').length} {t('payments')} {t('awaiting')} {t('processing')}</Text>
                  </Alert>

                  <Alert icon={<IconBan size={16} />} color="yellow">
                    <Text fw={500} size="sm">{t('disputed')} {t('payments')}</Text>
                    <Text size="xs">{payments.filter(p => p.status === 'disputed').length} {t('payments')} {t('under')} {t('dispute')}</Text>
                  </Alert>

                  <Alert icon={<IconCurrencyDollar size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('overdue')} {t('payments')}</Text>
                    <Text size="xs">{payments.filter(p => p.status === 'overdue').length} {t('payments')} {t('are')} {t('overdue')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Payments */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Group justify="space-between" mb="md">
                  <Title order={4}>{t('recent')} {t('payments')}</Title>
                  <Button variant="subtle" size="xs" rightSection={<IconEye size={14} />}>
                    {t('viewAll')}
                  </Button>
                </Group>

                <Stack gap="sm">
                  {payments.slice(0, 4).map((payment) => (
                    <Paper key={payment.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getMethodColor(payment.payment_method)} radius="xl" size="sm">
                            {getInitials(payment.customer_name)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{payment.customer_name}</Text>
                            <Group gap="xs">
                              <Badge color={getTypeColor(payment.payment_type)} variant="light" size="xs">
                                {t(payment.payment_type)}
                              </Badge>
                              <Text size="xs" c="dimmed">{payment.payment_date}</Text>
                            </Group>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Text fw={500} size="sm">{payment.currency} {payment.amount.toLocaleString()}</Text>
                          <Badge color={getStatusColor(payment.status)} size="xs">
                            {t(payment.status)}
                          </Badge>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>

          {/* Payment Methods Overview */}
          <Grid mt="lg">
            <Grid.Col span={{ base: 12, md: 8 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('payment')} {t('methods')} {t('breakdown')}</Title>
                <Grid>
                  {['credit-card', 'digital-wallet', 'bank-transfer', 'cash', 'debit-card', 'cheque'].map((method) => {
                    const count = payments.filter(p => p.payment_method === method).length
                    const percentage = payments.length > 0 ? (count / payments.length) * 100 : 0
                    return (
                      <Grid.Col key={method} span={4}>
                        <Paper p="sm" withBorder>
                          <Group justify="space-between" mb="xs">
                            <Text size="sm" fw={500}>{t(method)}</Text>
                            <Badge color={getMethodColor(method)} variant="light" size="sm">
                              {count}
                            </Badge>
                          </Group>
                          <Progress value={percentage} color={getMethodColor(method)} size="sm" />
                          <Text size="xs" c="dimmed" mt="xs">{percentage.toFixed(1)}%</Text>
                        </Paper>
                      </Grid.Col>
                    )
                  })}
                </Grid>
              </Card>
            </Grid.Col>

            <Grid.Col span={{ base: 12, md: 4 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('quick')} {t('actions')}</Title>
                <Stack gap="sm">
                  <Button variant="light" fullWidth leftSection={<IconPlus size={16} />} onClick={() => setCreateModalOpen(true)}>
                    {t('recordPayment')}
                  </Button>
                  <Button variant="light" fullWidth leftSection={<IconDownload size={16} />}>
                    {t('exportPayments')}
                  </Button>
                  <Button variant="light" fullWidth leftSection={<IconRefresh size={16} />}>
                    {t('syncPayments')}
                  </Button>
                  <Button variant="light" fullWidth leftSection={<IconSettings size={16} />}>
                    {t('paymentSettings')}
                  </Button>
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="payments" pt="md">
          {/* Advanced Filters */}
          <Card withBorder mb="lg">
            <Group justify="space-between" mb="md">
              <Title order={4}>{t('payment')} {t('filters')}</Title>
              <Button variant="light" size="xs" leftSection={<IconX size={14} />}>
                {t('clearFilters')}
              </Button>
            </Group>

            <Grid>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <TextInput
                  placeholder={t('searchPayments')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 2 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'completed', label: t('completed') },
                    { value: 'pending', label: t('pending') },
                    { value: 'processing', label: t('processing') },
                    { value: 'failed', label: t('failed') },
                    { value: 'refunded', label: t('refunded') },
                    { value: 'disputed', label: t('disputed') },
                    { value: 'cancelled', label: t('cancelled') },
                    { value: 'overdue', label: t('overdue') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 2 }}>
                <Select
                  placeholder={t('allMethods')}
                  data={[
                    { value: '', label: t('allMethods') },
                    { value: 'credit-card', label: t('creditCard') },
                    { value: 'debit-card', label: t('debitCard') },
                    { value: 'cash', label: t('cash') },
                    { value: 'bank-transfer', label: t('bankTransfer') },
                    { value: 'digital-wallet', label: t('digitalWallet') },
                    { value: 'cheque', label: t('cheque') }
                  ]}
                  value={methodFilter}
                  onChange={(value) => setMethodFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 2 }}>
                <Select
                  placeholder={t('allTypes')}
                  data={[
                    { value: '', label: t('allTypes') },
                    { value: 'rental-fee', label: t('rentalFee') },
                    { value: 'deposit', label: t('deposit') },
                    { value: 'security-deposit', label: t('securityDeposit') },
                    { value: 'late-fee', label: t('lateFee') },
                    { value: 'damage-fee', label: t('damageFee') },
                    { value: 'fuel-charge', label: t('fuelCharge') },
                    { value: 'cleaning-fee', label: t('cleaningFee') },
                    { value: 'toll-fee', label: t('tollFee') },
                    { value: 'insurance-fee', label: t('insuranceFee') },
                    { value: 'cancellation-fee', label: t('cancellationFee') }
                  ]}
                  value={typeFilter}
                  onChange={(value) => setTypeFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" leftSection={<IconFilter size={14} />}>
                  {t('advancedFilters')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Comprehensive Payments Table */}
          <Card withBorder>
            <Group justify="space-between" mb="md">
              <Group>
                <Title order={4}>{t('payments')} {t('table')}</Title>
                <Badge variant="light">{filteredPayments.length} {t('payments')}</Badge>
              </Group>
              <Group>
                <Select
                  size="xs"
                  value={pageSize.toString()}
                  onChange={(value) => setPageSize(parseInt(value || '10'))}
                  data={[
                    { value: '10', label: '10 per page' },
                    { value: '25', label: '25 per page' },
                    { value: '50', label: '50 per page' },
                    { value: '100', label: '100 per page' }
                  ]}
                />
              </Group>
            </Group>

            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>
                    <Checkbox
                      checked={selectedPayments.length === paginatedPayments.length && paginatedPayments.length > 0}
                      indeterminate={selectedPayments.length > 0 && selectedPayments.length < paginatedPayments.length}
                      onChange={(event) => {
                        if (event.currentTarget.checked) {
                          setSelectedPayments(paginatedPayments.map(p => p.id))
                        } else {
                          setSelectedPayments([])
                        }
                      }}
                    />
                  </Table.Th>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('paymentDetails')}</Table.Th>
                  <Table.Th>{t('amount')}</Table.Th>
                  <Table.Th>{t('method')}</Table.Th>
                  <Table.Th>{t('type')}</Table.Th>
                  <Table.Th>{t('date')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {paginatedPayments.map((payment) => (
                  <Table.Tr key={payment.id}>
                    <Table.Td>
                      <Checkbox
                        checked={selectedPayments.includes(payment.id)}
                        onChange={(event) => {
                          if (event.currentTarget.checked) {
                            setSelectedPayments([...selectedPayments, payment.id])
                          } else {
                            setSelectedPayments(selectedPayments.filter(id => id !== payment.id))
                          }
                        }}
                      />
                    </Table.Td>
                    <Table.Td>
                      <Group>
                        <Avatar color={getMethodColor(payment.payment_method)} radius="xl" size="sm">
                          {getInitials(payment.customer_name)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{payment.customer_name}</Text>
                          <Text size="xs" c="dimmed">{payment.contract_number}</Text>
                          <Text size="xs" c="dimmed">{payment.vehicle_info}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{payment.payment_id}</Text>
                        <Text size="xs" c="dimmed">{payment.transaction_id}</Text>
                        <Text size="xs" c="dimmed">{payment.reference_number}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{payment.currency} {payment.amount.toLocaleString()}</Text>
                        <Text size="xs" c="dimmed">{t('net')}: {payment.currency} {payment.net_amount.toLocaleString()}</Text>
                        <Text size="xs" c="dimmed">{t('fees')}: {payment.currency} {payment.fees}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getMethodColor(payment.payment_method)} variant="light">
                        {t(payment.payment_method)}
                      </Badge>
                      <Text size="xs" c="dimmed" mt="xs">{payment.gateway}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getTypeColor(payment.payment_type)} variant="light">
                        {t(payment.payment_type)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text size="sm">{payment.payment_date}</Text>
                        <Text size="xs" c="dimmed">{t('due')}: {payment.due_date}</Text>
                        <Text size="xs" c="dimmed">{payment.location}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(payment.status)}>
                        {t(payment.status)}
                      </Badge>
                      {payment.receipt_sent && (
                        <Text size="xs" c="green" mt="xs">✓ {t('receiptSent')}</Text>
                      )}
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon
                          variant="light"
                          size="sm"
                          color="blue"
                          onClick={() => {
                            setSelectedPayment(payment)
                            setViewModalOpen(true)
                          }}
                        >
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon
                          variant="light"
                          size="sm"
                          color="green"
                          onClick={() => {
                            setSelectedPayment(payment)
                            setRefundModalOpen(true)
                          }}
                        >
                          <IconCreditCardOff size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm" color="purple">
                          <IconReceipt size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm" color="gray">
                          <IconDownload size={14} />
                        </ActionIcon>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>

            {/* Pagination */}
            <Group justify="space-between" mt="md">
              <Text size="sm" c="dimmed">
                {t('showing')} {((currentPage - 1) * pageSize) + 1} - {Math.min(currentPage * pageSize, filteredPayments.length)} {t('of')} {filteredPayments.length} {t('payments')}
              </Text>
              <Pagination
                value={currentPage}
                onChange={setCurrentPage}
                total={totalPages}
                size="sm"
              />
            </Group>
          </Card>
        </Tabs.Panel>

        <Tabs.Panel value="analytics" pt="md">
          <Grid>
            <Grid.Col span={12}>
              <Card withBorder>
                <Title order={4} mb="md">{t('paymentAnalytics')}</Title>
                <Text c="dimmed">{t('analyticsComingSoon')}</Text>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="reports" pt="md">
          <Grid>
            <Grid.Col span={12}>
              <Card withBorder>
                <Title order={4} mb="md">{t('paymentReports')}</Title>
                <Text c="dimmed">{t('reportsComingSoon')}</Text>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>
      </Tabs>

      {/* View Payment Modal */}
      <Modal
        opened={viewModalOpen}
        onClose={() => setViewModalOpen(false)}
        title={selectedPayment ? `${t('paymentDetails')}: ${selectedPayment.payment_id}` : t('paymentDetails')}
        size="lg"
      >
        {selectedPayment && (
          <Stack>
            <Alert color="blue" icon={<IconCreditCard size={16} />}>
              <Text fw={500}>{t('paymentInformation')}</Text>
              <Text size="sm">{selectedPayment.payment_id} • {t(selectedPayment.status)}</Text>
            </Alert>

            <Grid>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">{t('customer')}</Text>
                <Text fw={500}>{selectedPayment.customer_name}</Text>
                <Text size="xs" c="dimmed">{selectedPayment.customer_email}</Text>
                <Text size="xs" c="dimmed">{selectedPayment.customer_phone}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">{t('contract')}</Text>
                <Text fw={500}>{selectedPayment.contract_number}</Text>
                <Text size="xs" c="dimmed">{selectedPayment.vehicle_info}</Text>
              </Grid.Col>
            </Grid>

            <Divider label={t('paymentDetails')} />

            <Grid>
              <Grid.Col span={4}>
                <Text size="sm" c="dimmed">{t('amount')}</Text>
                <Text fw={500} size="lg">{selectedPayment.currency} {selectedPayment.amount.toLocaleString()}</Text>
              </Grid.Col>
              <Grid.Col span={4}>
                <Text size="sm" c="dimmed">{t('netAmount')}</Text>
                <Text fw={500}>{selectedPayment.currency} {selectedPayment.net_amount.toLocaleString()}</Text>
              </Grid.Col>
              <Grid.Col span={4}>
                <Text size="sm" c="dimmed">{t('fees')}</Text>
                <Text fw={500}>{selectedPayment.currency} {selectedPayment.fees}</Text>
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">{t('paymentMethod')}</Text>
                <Badge color={getMethodColor(selectedPayment.payment_method)} variant="light">
                  {t(selectedPayment.payment_method)}
                </Badge>
                <Text size="xs" c="dimmed" mt="xs">{selectedPayment.gateway}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">{t('paymentType')}</Text>
                <Badge color={getTypeColor(selectedPayment.payment_type)} variant="light">
                  {t(selectedPayment.payment_type)}
                </Badge>
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">{t('paymentDate')}</Text>
                <Text fw={500}>{selectedPayment.payment_date}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">{t('dueDate')}</Text>
                <Text fw={500}>{selectedPayment.due_date}</Text>
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">{t('transactionId')}</Text>
                <Text fw={500}>{selectedPayment.transaction_id}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">{t('referenceNumber')}</Text>
                <Text fw={500}>{selectedPayment.reference_number}</Text>
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">{t('location')}</Text>
                <Text fw={500}>{selectedPayment.location}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">{t('processedBy')}</Text>
                <Text fw={500}>{selectedPayment.processed_by}</Text>
              </Grid.Col>
            </Grid>

            {selectedPayment.notes && (
              <>
                <Divider label={t('notes')} />
                <Text size="sm">{selectedPayment.notes}</Text>
              </>
            )}

            {selectedPayment.status === 'refunded' && (
              <>
                <Divider label={t('refundInformation')} />
                <Grid>
                  <Grid.Col span={6}>
                    <Text size="sm" c="dimmed">{t('refundAmount')}</Text>
                    <Text fw={500}>{selectedPayment.currency} {selectedPayment.refund_amount?.toLocaleString()}</Text>
                  </Grid.Col>
                  <Grid.Col span={6}>
                    <Text size="sm" c="dimmed">{t('refundDate')}</Text>
                    <Text fw={500}>{selectedPayment.refund_date}</Text>
                  </Grid.Col>
                </Grid>
                <Text size="sm" c="dimmed">{t('refundReason')}</Text>
                <Text size="sm">{selectedPayment.refund_reason}</Text>
              </>
            )}

            {selectedPayment.status === 'disputed' && (
              <>
                <Divider label={t('disputeInformation')} />
                <Text size="sm" c="dimmed">{t('disputeReason')}</Text>
                <Text size="sm">{selectedPayment.dispute_reason}</Text>
              </>
            )}

            <Divider />

            <Group justify="space-between">
              <Group>
                <Badge color={getStatusColor(selectedPayment.status)}>
                  {t(selectedPayment.status)}
                </Badge>
                {selectedPayment.receipt_sent && (
                  <Badge color="green" variant="light">
                    {t('receiptSent')}
                  </Badge>
                )}
              </Group>
              <Group>
                <Button variant="light" leftSection={<IconPrinter size={16} />}>
                  {t('printReceipt')}
                </Button>
                <Button variant="light" leftSection={<IconMail size={16} />}>
                  {t('emailReceipt')}
                </Button>
                <Button variant="light" leftSection={<IconCreditCardOff size={16} />}>
                  {t('processRefund')}
                </Button>
              </Group>
            </Group>
          </Stack>
        )}
      </Modal>

      {/* Create Payment Modal */}
      <Modal
        opened={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
        title={t('recordNewPayment')}
        size="lg"
      >
        <Stack>
          <Alert color="blue" icon={<IconCreditCard size={16} />}>
            <Text fw={500}>{t('recordPaymentInfo')}</Text>
            <Text size="sm">{t('recordPaymentDescription')}</Text>
          </Alert>

          <Grid>
            <Grid.Col span={6}>
              <Select
                label={t('customer')}
                placeholder={t('selectCustomer')}
                data={[
                  { value: '1', label: 'Ahmed Al-Rashid - CON-2024-001' },
                  { value: '2', label: 'Sarah Johnson - CON-2024-002' },
                  { value: '3', label: 'Mohammed Hassan - CON-2024-003' },
                  { value: '4', label: 'Emily Chen - CON-2024-004' },
                  { value: '5', label: 'David Wilson - CON-2024-005' }
                ]}
                searchable
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('contractNumber')}
                placeholder={t('enterContractNumber')}
                required
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={4}>
              <NumberInput
                label={t('amount')}
                placeholder="0.00"
                min={0}
                decimalScale={2}
                fixedDecimalScale
                required
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <Select
                label={t('currency')}
                placeholder={t('selectCurrency')}
                data={[
                  { value: 'AED', label: 'AED - UAE Dirham' },
                  { value: 'USD', label: 'USD - US Dollar' },
                  { value: 'EUR', label: 'EUR - Euro' },
                  { value: 'GBP', label: 'GBP - British Pound' }
                ]}
                defaultValue="AED"
                required
              />
            </Grid.Col>
            <Grid.Col span={4}>
              <NumberInput
                label={t('processingFees')}
                placeholder="0.00"
                min={0}
                decimalScale={2}
                fixedDecimalScale
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <Select
                label={t('paymentMethod')}
                placeholder={t('selectPaymentMethod')}
                data={[
                  { value: 'credit-card', label: t('creditCard') },
                  { value: 'debit-card', label: t('debitCard') },
                  { value: 'cash', label: t('cash') },
                  { value: 'bank-transfer', label: t('bankTransfer') },
                  { value: 'digital-wallet', label: t('digitalWallet') },
                  { value: 'cheque', label: t('cheque') }
                ]}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('paymentType')}
                placeholder={t('selectPaymentType')}
                data={[
                  { value: 'rental-fee', label: t('rentalFee') },
                  { value: 'deposit', label: t('deposit') },
                  { value: 'security-deposit', label: t('securityDeposit') },
                  { value: 'late-fee', label: t('lateFee') },
                  { value: 'damage-fee', label: t('damageFee') },
                  { value: 'fuel-charge', label: t('fuelCharge') },
                  { value: 'cleaning-fee', label: t('cleaningFee') },
                  { value: 'toll-fee', label: t('tollFee') },
                  { value: 'insurance-fee', label: t('insuranceFee') },
                  { value: 'cancellation-fee', label: t('cancellationFee') }
                ]}
                required
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <DateInput
                label={t('paymentDate')}
                placeholder={t('selectPaymentDate')}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <DateInput
                label={t('dueDate')}
                placeholder={t('selectDueDate')}
                required
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <TextInput
                label={t('transactionId')}
                placeholder={t('enterTransactionId')}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <TextInput
                label={t('referenceNumber')}
                placeholder={t('enterReferenceNumber')}
              />
            </Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>
              <Select
                label={t('location')}
                placeholder={t('selectLocation')}
                data={[
                  { value: 'dubai-airport', label: 'Dubai Airport' },
                  { value: 'abu-dhabi-marina', label: 'Abu Dhabi Marina' },
                  { value: 'sharjah-city', label: 'Sharjah City Center' },
                  { value: 'dubai-marina', label: 'Dubai Marina' },
                  { value: 'al-ain', label: 'Al Ain' }
                ]}
                required
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <Select
                label={t('paymentGateway')}
                placeholder={t('selectGateway')}
                data={[
                  { value: 'stripe', label: 'Stripe' },
                  { value: 'paypal', label: 'PayPal' },
                  { value: 'square', label: 'Square' },
                  { value: 'bank-transfer', label: 'Bank Transfer' },
                  { value: 'cash', label: 'Cash' }
                ]}
              />
            </Grid.Col>
          </Grid>

          <Textarea
            label={t('notes')}
            placeholder={t('enterPaymentNotes')}
            rows={3}
          />

          <Group>
            <Checkbox label={t('sendReceiptToCustomer')} defaultChecked />
            <Checkbox label={t('markAsProcessed')} />
          </Group>

          <Divider />

          <Group justify="space-between">
            <Text size="sm" c="dimmed">{t('paymentWillBeRecorded')}</Text>
            <Group>
              <Button variant="light" onClick={() => setCreateModalOpen(false)}>
                {t('cancel')}
              </Button>
              <Button leftSection={<IconCheck size={16} />} onClick={() => setCreateModalOpen(false)}>
                {t('recordPayment')}
              </Button>
            </Group>
          </Group>
        </Stack>
      </Modal>

      {/* Refund Payment Modal */}
      <Modal
        opened={refundModalOpen}
        onClose={() => setRefundModalOpen(false)}
        title={selectedPayment ? `${t('processRefund')}: ${selectedPayment.payment_id}` : t('processRefund')}
        size="md"
      >
        {selectedPayment && (
          <Stack>
            <Alert color="orange" icon={<IconCreditCardOff size={16} />}>
              <Text fw={500}>{t('refundWarning')}</Text>
              <Text size="sm">{t('refundWarningDescription')}</Text>
            </Alert>

            <Paper p="md" withBorder>
              <Text size="sm" c="dimmed">{t('originalPayment')}</Text>
              <Text fw={500}>{selectedPayment.payment_id}</Text>
              <Text size="sm">{selectedPayment.customer_name}</Text>
              <Text fw={500} size="lg">{selectedPayment.currency} {selectedPayment.amount.toLocaleString()}</Text>
            </Paper>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('refundAmount')}
                  placeholder="0.00"
                  min={0}
                  max={selectedPayment.amount}
                  decimalScale={2}
                  fixedDecimalScale
                  defaultValue={selectedPayment.amount}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('refundMethod')}
                  placeholder={t('selectRefundMethod')}
                  data={[
                    { value: 'original-method', label: t('originalPaymentMethod') },
                    { value: 'bank-transfer', label: t('bankTransfer') },
                    { value: 'cash', label: t('cash') },
                    { value: 'cheque', label: t('cheque') }
                  ]}
                  defaultValue="original-method"
                  required
                />
              </Grid.Col>
            </Grid>

            <Textarea
              label={t('refundReason')}
              placeholder={t('enterRefundReason')}
              required
              rows={3}
            />

            <Group>
              <Checkbox label={t('notifyCustomer')} defaultChecked />
              <Checkbox label={t('sendRefundReceipt')} defaultChecked />
            </Group>

            <Divider />

            <Group justify="space-between">
              <Text size="sm" c="dimmed">{t('refundWillBeProcessed')}</Text>
              <Group>
                <Button variant="light" onClick={() => setRefundModalOpen(false)}>
                  {t('cancel')}
                </Button>
                <Button color="orange" leftSection={<IconCreditCardOff size={16} />} onClick={() => setRefundModalOpen(false)}>
                  {t('processRefund')}
                </Button>
              </Group>
            </Group>
          </Stack>
        )}
      </Modal>

      {/* Batch Operations Modal */}
      <Modal
        opened={batchModalOpen}
        onClose={() => setBatchModalOpen(false)}
        title="Create New Batch Operation"
        size="lg"
      >
        <Stack>
          <Alert color="blue" icon={<IconStack size={16} />}>
            <Text fw={500}>Batch Payment Operations</Text>
            <Text size="sm">Process multiple payments simultaneously with bulk operations</Text>
          </Alert>

          <Grid>
            <Grid.Col span={12}>
              <Text size="sm" fw={500} mb="xs">Selected Payments: {selectedPayments.length}</Text>
              {selectedPayments.length === 0 ? (
                <Alert color="orange">
                  <Text size="sm">Please select payments from the table to perform batch operations</Text>
                </Alert>
              ) : (
                <Text size="sm" c="dimmed">
                  {selectedPayments.length} payment{selectedPayments.length > 1 ? 's' : ''} selected for batch processing
                </Text>
              )}
            </Grid.Col>
          </Grid>

          <Select
            label="Batch Operation Type"
            placeholder="Select operation to perform"
            data={[
              { value: 'export', label: 'Export Selected Payments' },
              { value: 'send-receipts', label: 'Send Receipts to Customers' },
              { value: 'mark-completed', label: 'Mark as Completed' },
              { value: 'mark-pending', label: 'Mark as Pending' },
              { value: 'process-refunds', label: 'Process Bulk Refunds' },
              { value: 'generate-report', label: 'Generate Batch Report' },
              { value: 'update-status', label: 'Update Payment Status' },
              { value: 'send-reminders', label: 'Send Payment Reminders' }
            ]}
            value={batchOperation}
            onChange={(value) => setBatchOperation(value || '')}
            required
          />

          {batchOperation === 'export' && (
            <Select
              label="Export Format"
              placeholder="Select export format"
              data={[
                { value: 'csv', label: 'CSV - Comma Separated Values' },
                { value: 'excel', label: 'Excel - Microsoft Excel' },
                { value: 'pdf', label: 'PDF - Portable Document Format' },
                { value: 'json', label: 'JSON - JavaScript Object Notation' }
              ]}
            />
          )}

          {batchOperation === 'process-refunds' && (
            <Grid>
              <Grid.Col span={6}>
                <Select
                  label="Refund Method"
                  placeholder="Select refund method"
                  data={[
                    { value: 'original-method', label: 'Original Payment Method' },
                    { value: 'bank-transfer', label: 'Bank Transfer' },
                    { value: 'cash', label: 'Cash' },
                    { value: 'cheque', label: 'Cheque' }
                  ]}
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <NumberInput
                  label="Refund Percentage"
                  placeholder="100"
                  min={1}
                  max={100}
                  suffix="%"
                  defaultValue={100}
                />
              </Grid.Col>
            </Grid>
          )}

          {batchOperation === 'update-status' && (
            <Select
              label="New Status"
              placeholder="Select new status"
              data={[
                { value: 'completed', label: 'Completed' },
                { value: 'pending', label: 'Pending' },
                { value: 'processing', label: 'Processing' },
                { value: 'failed', label: 'Failed' },
                { value: 'cancelled', label: 'Cancelled' }
              ]}
            />
          )}

          <Textarea
            label="Operation Notes"
            placeholder="Add notes for this batch operation..."
            rows={3}
          />

          <Group>
            <Checkbox label="Send notification to customers" />
            <Checkbox label="Generate operation log" defaultChecked />
          </Group>

          <Divider />

          <Group justify="space-between">
            <Text size="sm" c="dimmed">
              This operation will affect {selectedPayments.length} payment{selectedPayments.length > 1 ? 's' : ''}
            </Text>
            <Group>
              <Button variant="light" onClick={() => setBatchModalOpen(false)}>
                Cancel
              </Button>
              <Button
                leftSection={<IconStack size={16} />}
                disabled={selectedPayments.length === 0 || !batchOperation}
                onClick={() => {
                  // Handle batch operation
                  console.log('Batch operation:', batchOperation, 'on payments:', selectedPayments)
                  setBatchModalOpen(false)
                  setSelectedPayments([])
                  setBatchOperation('')
                }}
              >
                Execute Batch Operation
              </Button>
            </Group>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}

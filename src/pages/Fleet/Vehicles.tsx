import { useState, useEffect, useMemo } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Checkbox,
  Menu,
  Modal,
  NumberInput,
  Textarea,
  Divider,
  Tabs,
  Tooltip,
  Pagination,
  Center,
  MultiSelect,
  RingProgress,
  ThemeIcon
} from '@mantine/core'
import { notifications } from '@mantine/notifications'
import {
  IconPlus,
  IconSearch,
  IconFilter,
  IconEdit,
  IconEye,
  IconTrash,
  IconCar,
  IconDots,
  IconDownload,
  IconUpload,
  IconTool,
  IconAlertTriangle,
  IconSortAscending,
  IconRefresh,
  IconPrinter,
  IconQrcode,
  IconMapPin,
  IconChartBar
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { vehicleService } from '../../services/vehicleService'
import { ErrorBoundary } from '../../components/Common/ErrorBoundary'
import { LoadingState } from '../../components/Common/LoadingState'
import { useAppStore } from '../../store/useAppStore'
import { PageHeader } from '../../components/Common/PageHeader'

interface VehicleTableData {
  id: string
  make: string
  model: string
  year: number
  plate_number: string
  category_name?: string
  location_name?: string
  status: 'available' | 'rented' | 'maintenance' | 'out_of_service' | 'reserved'
  daily_rate: number
  current_mileage: number
  fuel_type: string
  transmission: string
  color: string
  vin: string
  fuel_level: number
  insurance_expiry: string
  registration_expiry: string
  last_service_date?: string
  next_service_due?: string
  features?: string
  notes?: string
  created_at: string
  updated_at: string
}

export function Vehicles() {
  const { t } = useTranslation()

  // Get real data from store
  const { vehicles: storeVehicles, categories, locations } = useAppStore()

  // State management
  const [error] = useState<string | null>(null)

  // Filters and search
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string[]>([])
  const [categoryFilter, setCategoryFilter] = useState<string[]>([])
  const [locationFilter, setLocationFilter] = useState('')

  // Sorting and pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)

  // Selection and bulk actions
  const [selectedVehicles, setSelectedVehicles] = useState<string[]>([])

  // Modals and forms
  const [activeTab, setActiveTab] = useState('overview')
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [selectedVehicle, setSelectedVehicle] = useState<VehicleTableData | null>(null)

  // Advanced filters
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)

  // Transform store vehicles to table format
  const displayVehicles: VehicleTableData[] = useMemo(() => {
    if (!storeVehicles || storeVehicles.length === 0) {
      return []
    }

    return storeVehicles.map((vehicle) => ({
      id: vehicle.id,
      make: vehicle.make,
      model: vehicle.model,
      year: vehicle.year,
      plate_number: vehicle.plate_number,
      category_name: categories?.find(c => c.id === vehicle.category_id)?.name || 'N/A',
      location_name: locations?.find(l => l.id === vehicle.location_id)?.name || 'N/A',
      status: vehicle.status,
      daily_rate: vehicle.daily_rate || 0,
      current_mileage: vehicle.current_mileage || 0,
      fuel_type: vehicle.fuel_type || 'gasoline',
      transmission: vehicle.transmission || 'automatic',
      color: vehicle.color || 'Unknown',
      vin: vehicle.vin || 'N/A',
      fuel_level: vehicle.fuel_level || 100,
      insurance_expiry: vehicle.insurance_expiry ? new Date(vehicle.insurance_expiry).toLocaleDateString() : 'N/A',
      registration_expiry: vehicle.registration_expiry ? new Date(vehicle.registration_expiry).toLocaleDateString() : 'N/A',
      last_service_date: vehicle.last_service_date ? new Date(vehicle.last_service_date).toLocaleDateString() : undefined,
      next_service_due: vehicle.next_service_due ? new Date(vehicle.next_service_due).toLocaleDateString() : undefined,
      features: Array.isArray(vehicle.features) ? JSON.stringify(vehicle.features) : (vehicle.features ? String(vehicle.features) : ''),
      notes: vehicle.notes || '',
      created_at: new Date(vehicle.created_at).toISOString(),
      updated_at: new Date(vehicle.updated_at).toISOString()
    }))
  }, [storeVehicles, categories, locations])

  // Calculate real stats from store data
  const displayStats = useMemo(() => {
    const total = storeVehicles.length
    const available = storeVehicles.filter(v => v.status === 'available').length
    const rented = storeVehicles.filter(v => v.status === 'rented').length
    const maintenance = storeVehicles.filter(v => v.status === 'maintenance').length

    return [
      { label: t('totalVehicles'), value: total.toString(), color: 'blue' },
      { label: t('available'), value: available.toString(), color: 'green' },
      { label: t('rented'), value: rented.toString(), color: 'orange' },
      { label: t('inMaintenance'), value: maintenance.toString(), color: 'red' }
    ]
  }, [storeVehicles, t])

  // Update pagination based on real data
  useEffect(() => {
    setTotalItems(displayVehicles.length)
    setTotalPages(Math.ceil(displayVehicles.length / itemsPerPage))
  }, [displayVehicles.length, itemsPerPage])

  // Real-time data - no more mock data needed!

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'green'
      case 'rented': return 'orange'
      case 'maintenance': return 'red'
      case 'out-of-service': return 'gray'
      default: return 'blue'
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedVehicles(displayVehicles.map(v => v.id))
    } else {
      setSelectedVehicles([])
    }
  }

  const handleSelectVehicle = (vehicleId: string, checked: boolean) => {
    if (checked) {
      setSelectedVehicles(prev => [...prev, vehicleId])
    } else {
      setSelectedVehicles(prev => prev.filter(id => id !== vehicleId))
    }
  }

  const filteredVehicles = displayVehicles.filter(vehicle => {
    const matchesSearch = vehicle.make.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         vehicle.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         vehicle.plate_number.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = statusFilter.length === 0 || statusFilter.includes(vehicle.status)
    const matchesCategory = categoryFilter.length === 0 || (vehicle.category_name && categoryFilter.includes(vehicle.category_name))
    const matchesLocation = !locationFilter || (vehicle.location_name && vehicle.location_name.toLowerCase().includes(locationFilter.toLowerCase()))

    return matchesSearch && matchesStatus && matchesCategory && matchesLocation
  })

  // Real-time data from store - no loading states needed

  return (
    <ErrorBoundary>
      <Stack gap="lg">
        <PageHeader
          title={t('vehicleManagement')}
          description={t('manageYourVehicleInventory')}
          actions={
            <Group>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                {t('export')}
              </Button>
              <Button variant="light" leftSection={<IconUpload size={16} />}>
                {t('import')}
              </Button>
              <Button
                leftSection={<IconPlus size={16} />}
                onClick={() => setAddModalOpen(true)}
              >
                {t('addVehicle')}
              </Button>
            </Group>
          }
        />

        <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>

          <Tabs.List mb="lg">
            <Tabs.Tab value="overview" leftSection={<IconChartBar size={16} />}>
              {t('overview')}
            </Tabs.Tab>
            <Tabs.Tab value="vehicles" leftSection={<IconCar size={16} />}>
              {t('allVehicles')} ({filteredVehicles.length})
            </Tabs.Tab>
            <Tabs.Tab value="maintenance" leftSection={<IconTool size={16} />}>
              {t('maintenance')}
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="overview">
            {/* Stats */}
            <Grid mb="lg">
              {displayStats.map((stat: any) => (
                <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                  <Paper p="md" withBorder>
                    <Group justify="space-between">
                      <div>
                        <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                          {stat.label}
                        </Text>
                        <Text fw={700} size="xl">
                          {stat.value}
                        </Text>
                      </div>
                      <ThemeIcon size="xl" variant="light" color={stat.color}>
                        <IconCar size={24} />
                      </ThemeIcon>
                    </Group>
                  </Paper>
                </Grid.Col>
              ))}
            </Grid>

            {/* Quick Actions */}
            <Grid mb="lg">
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Card withBorder>
                  <Title order={4} mb="md">{t('quickActions')}</Title>
                  <Stack gap="sm">
                    <Button variant="light" fullWidth leftSection={<IconPlus size={16} />}>
                      {t('addNewVehicle')}
                    </Button>
                    <Button variant="light" fullWidth leftSection={<IconUpload size={16} />}>
                      {t('bulkImport')}
                    </Button>
                    <Button variant="light" fullWidth leftSection={<IconTool size={16} />}>
                      {t('scheduleMaintenace')}
                    </Button>
                  </Stack>
                </Card>
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 6 }}>
                <Card withBorder>
                  <Title order={4} mb="md">{t('recentActivity')}</Title>
                  <Text c="dimmed" size="sm">{t('recentActivityWillBeShownHere')}</Text>
                </Card>
              </Grid.Col>
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value="vehicles">
            {/* Filters */}
            <Card withBorder mb="lg">
              <Grid>
                <Grid.Col span={{ base: 12, md: 4 }}>
                  <TextInput
                    placeholder={t('searchVehicles')}
                    leftSection={<IconSearch size={16} />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <MultiSelect
                    placeholder={t('allStatus')}
                    data={[
                      { value: 'available', label: t('available') },
                      { value: 'rented', label: t('rented') },
                      { value: 'maintenance', label: t('maintenance') },
                      { value: 'out_of_service', label: t('outOfService') },
                      { value: 'reserved', label: t('reserved') }
                    ]}
                    value={statusFilter}
                    onChange={setStatusFilter}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <MultiSelect
                    placeholder={t('allCategories')}
                    data={categories?.map(cat => ({ value: cat.name, label: cat.name })) || []}
                    value={categoryFilter}
                    onChange={setCategoryFilter}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <TextInput
                    placeholder={t('location')}
                    leftSection={<IconMapPin size={16} />}
                    value={locationFilter}
                    onChange={(e) => setLocationFilter(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Group>
                    <Button
                      variant="light"
                      leftSection={<IconFilter size={16} />}
                      onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                    >
                      {t('filters')}
                    </Button>
                    <Button variant="light" leftSection={<IconRefresh size={16} />} onClick={() => window.location.reload()}>
                      {t('refresh')}
                    </Button>
                  </Group>
                </Grid.Col>
              </Grid>
            </Card>

            {/* Bulk Actions */}
            {selectedVehicles.length > 0 && (
              <Card withBorder mb="md" bg="blue.0">
                <Group justify="space-between">
                  <Text fw={500}>
                    {selectedVehicles.length} {t('vehiclesSelected')}
                  </Text>
                  <Group>
                    <Button variant="light" size="sm">
                      {t('bulkEdit')}
                    </Button>
                    <Button variant="light" size="sm" color="red">
                      {t('bulkDelete')}
                    </Button>
                    <Button variant="light" size="sm" leftSection={<IconDownload size={14} />}>
                      {t('export')}
                    </Button>
                  </Group>
                </Group>
              </Card>
            )}

            {/* Vehicle Table */}
            <Card withBorder>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>
                      <Checkbox
                        checked={selectedVehicles.length === filteredVehicles.length && filteredVehicles.length > 0}
                        indeterminate={selectedVehicles.length > 0 && selectedVehicles.length < filteredVehicles.length}
                        onChange={(e) => handleSelectAll(e.currentTarget.checked)}
                      />
                    </Table.Th>
                    <Table.Th>
                      <Group gap="xs">
                        <Text>{t('vehicle')}</Text>
                        <ActionIcon size="xs" variant="transparent">
                          <IconSortAscending size={12} />
                        </ActionIcon>
                      </Group>
                    </Table.Th>
                    <Table.Th>{t('plateNumber')}</Table.Th>
                    <Table.Th>{t('category')}</Table.Th>
                    <Table.Th>{t('location')}</Table.Th>
                    <Table.Th>{t('status')}</Table.Th>
                    <Table.Th>{t('dailyRate')}</Table.Th>
                    <Table.Th>{t('fuelLevel')}</Table.Th>
                    <Table.Th>{t('actions')}</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {filteredVehicles.map((vehicle) => (
                    <Table.Tr key={vehicle.id}>
                      <Table.Td>
                        <Checkbox
                          checked={selectedVehicles.includes(vehicle.id)}
                          onChange={(e) => handleSelectVehicle(vehicle.id, e.currentTarget.checked)}
                        />
                      </Table.Td>
                      <Table.Td>
                        <Group>
                          <div>
                            <Text fw={500}>{vehicle.make} {vehicle.model} {vehicle.year}</Text>
                            <Text size="sm" c="dimmed">{vehicle.transmission} • {vehicle.fuel_type}</Text>
                          </div>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Text fw={700}>{vehicle.plate_number}</Text>
                      </Table.Td>
                      <Table.Td>{vehicle.category_name || 'N/A'}</Table.Td>
                      <Table.Td>{vehicle.location_name || 'N/A'}</Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(vehicle.status)} variant="light">
                          {t(vehicle.status)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text fw={700}>${vehicle.daily_rate}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <RingProgress
                            size={30}
                            thickness={3}
                            sections={[{ value: vehicle.fuel_level, color: vehicle.fuel_level > 50 ? 'green' : vehicle.fuel_level > 25 ? 'orange' : 'red' }]}
                          />
                          <Text size="xs">{vehicle.fuel_level}%</Text>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <Tooltip label={t('viewDetails')}>
                            <ActionIcon
                              variant="light"
                              size="sm"
                              onClick={() => {
                                setSelectedVehicle(vehicle)
                                setViewModalOpen(true)
                              }}
                            >
                              <IconEye size={14} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label={t('edit')}>
                            <ActionIcon
                              variant="light"
                              size="sm"
                              onClick={() => {
                                setSelectedVehicle(vehicle)
                                setEditModalOpen(true)
                              }}
                            >
                              <IconEdit size={14} />
                            </ActionIcon>
                          </Tooltip>
                          <Menu>
                            <Menu.Target>
                              <ActionIcon variant="light" size="sm">
                                <IconDots size={14} />
                              </ActionIcon>
                            </Menu.Target>
                            <Menu.Dropdown>
                              <Menu.Item leftSection={<IconQrcode size={14} />}>
                                {t('generateQR')}
                              </Menu.Item>
                              <Menu.Item leftSection={<IconPrinter size={14} />}>
                                {t('printLabel')}
                              </Menu.Item>
                              <Menu.Item leftSection={<IconTool size={14} />}>
                                {t('scheduleMaintenance')}
                              </Menu.Item>
                              <Menu.Divider />
                              <Menu.Item leftSection={<IconTrash size={14} />} color="red">
                                {t('delete')}
                              </Menu.Item>
                            </Menu.Dropdown>
                          </Menu>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Text size="sm" c="dimmed">
                  {t('showing')} {Math.min((currentPage - 1) * itemsPerPage + 1, totalItems)} - {Math.min(currentPage * itemsPerPage, totalItems)} {t('of')} {totalItems} {t('vehicles')}
                </Text>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="maintenance">
            <Card withBorder>
              <Title order={4} mb="md">{t('maintenanceSchedule')}</Title>
              <Text c="dimmed">{t('maintenanceScheduleWillBeImplementedHere')}</Text>
            </Card>
          </Tabs.Panel>
        </Tabs>

        {/* Add Vehicle Modal */}
        <Modal
          opened={addModalOpen}
          onClose={() => setAddModalOpen(false)}
          title={t('addNewVehicle')}
          size="lg"
        >
          <Stack>
            <Grid>
              <Grid.Col span={6}>
                <TextInput
                  label={t('make')}
                  placeholder={t('enterMake')}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('model')}
                  placeholder={t('enterModel')}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('year')}
                  placeholder={t('enterYear')}
                  min={1990}
                  max={new Date().getFullYear() + 1}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('plateNumber')}
                  placeholder={t('enterPlateNumber')}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <Select
                  label={t('category')}
                  placeholder={t('selectCategory')}
                  data={categories?.map(cat => ({ value: cat.id, label: cat.name })) || []}
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <TextInput
                  label={t('location')}
                  placeholder={t('enterLocation')}
                  required
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={6}>
                <NumberInput
                  label={t('dailyRate')}
                  placeholder={t('enterDailyRate')}
                  min={0}
                  prefix="$"
                  required
                />
              </Grid.Col>
              <Grid.Col span={6}>
                <Select
                  label={t('fuelType')}
                  placeholder={t('selectFuelType')}
                  data={[
                    { value: 'gasoline', label: 'Gasoline' },
                    { value: 'diesel', label: 'Diesel' },
                    { value: 'electric', label: 'Electric' },
                    { value: 'hybrid', label: 'Hybrid' }
                  ]}
                  required
                />
              </Grid.Col>
            </Grid>

            <Textarea
              label={t('notes')}
              placeholder={t('enterNotes')}
              rows={3}
            />

            <Divider />

            <Group justify="flex-end">
              <Button variant="light" onClick={() => setAddModalOpen(false)}>
                {t('cancel')}
              </Button>
              <Button onClick={() => setAddModalOpen(false)}>
                {t('addVehicle')}
              </Button>
            </Group>
          </Stack>
        </Modal>

        {/* View Vehicle Modal */}
        <Modal
          opened={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={selectedVehicle ? `${selectedVehicle.make} ${selectedVehicle.model}` : t('vehicleDetails')}
          size="lg"
        >
          {selectedVehicle && (
            <Stack>
              <Grid>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('plateNumber')}</Text>
                  <Text fw={500}>{selectedVehicle.plate_number}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('vin')}</Text>
                  <Text fw={500}>{selectedVehicle.vin}</Text>
                </Grid.Col>
              </Grid>

              <Grid>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('category')}</Text>
                  <Text fw={500}>{selectedVehicle.category_name || 'N/A'}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('status')}</Text>
                  <Badge color={getStatusColor(selectedVehicle.status)}>
                    {t(selectedVehicle.status)}
                  </Badge>
                </Grid.Col>
              </Grid>

              <Grid>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('dailyRate')}</Text>
                  <Text fw={500}>${selectedVehicle.daily_rate}</Text>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('mileage')}</Text>
                  <Text fw={500}>{selectedVehicle.current_mileage.toLocaleString()} km</Text>
                </Grid.Col>
              </Grid>

              <Grid>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('fuelLevel')}</Text>
                  <Group gap="xs">
                    <RingProgress
                      size={40}
                      thickness={4}
                      sections={[{ value: selectedVehicle.fuel_level, color: selectedVehicle.fuel_level > 50 ? 'green' : selectedVehicle.fuel_level > 25 ? 'orange' : 'red' }]}
                    />
                    <Text fw={500}>{selectedVehicle.fuel_level}%</Text>
                  </Group>
                </Grid.Col>
                <Grid.Col span={6}>
                  <Text size="sm" c="dimmed">{t('location')}</Text>
                  <Text fw={500}>{selectedVehicle.location_name || 'N/A'}</Text>
                </Grid.Col>
              </Grid>

              {selectedVehicle.notes && (
                <div>
                  <Text size="sm" c="dimmed">{t('notes')}</Text>
                  <Text>{selectedVehicle.notes}</Text>
                </div>
              )}

              <Divider />

              <Group justify="flex-end">
                <Button variant="light" onClick={() => setViewModalOpen(false)}>
                  {t('close')}
                </Button>
                <Button
                  onClick={() => {
                    setViewModalOpen(false)
                    setEditModalOpen(true)
                  }}
                  leftSection={<IconEdit size={16} />}
                >
                  {t('edit')}
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>

        {/* Edit Vehicle Modal */}
        <Modal
          opened={editModalOpen}
          onClose={() => setEditModalOpen(false)}
          title={selectedVehicle ? `${t('edit')} ${selectedVehicle.make} ${selectedVehicle.model}` : t('editVehicle')}
          size="lg"
        >
          {selectedVehicle && (
            <Stack>
              <Text c="dimmed">{t('editVehicleFormWillBeImplementedHere')}</Text>
              <Group justify="flex-end">
                <Button variant="light" onClick={() => setEditModalOpen(false)}>
                  {t('cancel')}
                </Button>
                <Button onClick={() => setEditModalOpen(false)}>
                  {t('saveChanges')}
                </Button>
              </Group>
            </Stack>
          )}
        </Modal>
      </Stack>
    </ErrorBoundary>
  )
}



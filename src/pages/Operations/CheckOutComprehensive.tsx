import { useState, useEffect } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Stack,
  Modal,
  Divider,
  Tabs,
  Avatar,
  Pagination,
  NumberInput,
  Textarea,
  Progress,
  Alert,
  Stepper,
  Checkbox,
  FileInput,
  Rating
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCheck,
  IconClock,
  IconDownload,
  IconEye,
  IconFilter,
  IconSearch,
  IconMapPin,
  IconRefresh,
  IconClipboardCheck,
  IconCamera,
  IconStar,
  IconCalculator,
  IconCreditCard
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'
import { ErrorBoundary } from '../../components/Common/ErrorBoundary'
import { LoadingState } from '../../components/Common/LoadingState'

interface CheckOutRentalData {
  id: string
  reservation_id: string
  reservation_number: string
  customer_id: string
  customer_name: string
  customer_email: string
  customer_phone: string
  vehicle_id: string
  vehicle_name: string
  vehicle_make: string
  vehicle_model: string
  vehicle_year: number
  plate_number: string
  vin_number: string
  pickup_date: string
  return_date: string
  actual_return_date?: string
  pickup_location: string
  return_location: string
  status: 'due_today' | 'overdue' | 'returned' | 'inspecting' | 'billing' | 'completed'
  checkout_status: 'pending' | 'inspection_started' | 'damage_assessed' | 'billing_calculated' | 'payment_processed' | 'completed'
  total_amount: number
  deposit_amount: number
  deposit_status: 'held' | 'released' | 'partial_release'
  rental_days: number
  actual_days: number
  daily_rate: number
  vehicle_condition_pickup: {
    fuel_level: number
    mileage: number
    exterior_condition: string
    interior_condition: string
    cleanliness_score: number
  }
  vehicle_condition_return: {
    fuel_level: number
    mileage: number
    exterior_damage: string[]
    interior_condition: string
    cleanliness_score: number
    damage_photos: string[]
    damage_cost: number
  }
  fuel_policy: 'full_to_full' | 'same_to_same' | 'prepaid'
  fuel_charges: number
  mileage_limit?: number
  mileage_overage: number
  mileage_charges: number
  late_return_hours: number
  late_return_charges: number
  cleaning_charges: number
  damage_charges: number
  additional_charges: number
  total_additional_charges: number
  final_amount: number
  customer_satisfaction: {
    rating: number
    feedback: string
    would_recommend: boolean
  }
  checkout_notes: string
  staff_signature?: string
  customer_signature?: string
  checkout_start_time?: string
  checkout_completion_time?: string
  assigned_staff: string
  created_at: string
  updated_at: string
}

export function CheckOutComprehensive() {
  const { t } = useTranslation()
  const [loading, setLoading] = useState(false)
  const [checkOutRentals, setCheckOutRentals] = useState<CheckOutRentalData[]>([])
  const [activeTab, setActiveTab] = useState('all-checkouts')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [locationFilter, setLocationFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  
  // Modal states
  const [viewModalOpen, setViewModalOpen] = useState(false)
  const [checkOutModalOpen, setCheckOutModalOpen] = useState(false)
  const [damageModalOpen, setDamageModalOpen] = useState(false)
  const [billingModalOpen, setBillingModalOpen] = useState(false)
  const [feedbackModalOpen, setFeedbackModalOpen] = useState(false)
  const [selectedRental, setSelectedRental] = useState<CheckOutRentalData | null>(null)
  const [checkOutStep, setCheckOutStep] = useState(0)

  useEffect(() => {
    loadCheckOutRentals()
  }, [])

  const loadCheckOutRentals = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual database call
      // const data = await checkOutService.getAllCheckOutRentals()
      setCheckOutRentals(mockCheckOutRentals)
    } catch (error) {
      console.error('Error loading check-out rentals:', error)
    } finally {
      setLoading(false)
    }
  }

  // Mock data for development - comprehensive check-out structure
  const mockCheckOutRentals: CheckOutRentalData[] = [
    {
      id: '1',
      reservation_id: '1',
      reservation_number: 'RES-2024-001',
      customer_id: '1',
      customer_name: 'Ahmed Al-Rashid',
      customer_email: '<EMAIL>',
      customer_phone: '+971501234567',
      vehicle_id: '1',
      vehicle_name: 'Toyota Camry 2023',
      vehicle_make: 'Toyota',
      vehicle_model: 'Camry',
      vehicle_year: 2023,
      plate_number: 'ABC-123',
      vin_number: 'JT2BF22K123456789',
      pickup_date: '2024-01-15T09:00:00Z',
      return_date: '2024-01-20T18:00:00Z',
      actual_return_date: '2024-01-20T17:30:00Z',
      pickup_location: 'Dubai Airport Terminal 1',
      return_location: 'Dubai Airport Terminal 1',
      status: 'returned',
      checkout_status: 'pending',
      total_amount: 630,
      deposit_amount: 500,
      deposit_status: 'held',
      rental_days: 5,
      actual_days: 5,
      daily_rate: 120,
      vehicle_condition_pickup: {
        fuel_level: 100,
        mileage: 15000,
        exterior_condition: 'excellent',
        interior_condition: 'excellent',
        cleanliness_score: 95
      },
      vehicle_condition_return: {
        fuel_level: 75,
        mileage: 15420,
        exterior_damage: [],
        interior_condition: 'good',
        cleanliness_score: 85,
        damage_photos: [],
        damage_cost: 0
      },
      fuel_policy: 'full_to_full',
      fuel_charges: 45,
      mileage_limit: 1000,
      mileage_overage: 0,
      mileage_charges: 0,
      late_return_hours: 0,
      late_return_charges: 0,
      cleaning_charges: 25,
      damage_charges: 0,
      additional_charges: 0,
      total_additional_charges: 70,
      final_amount: 70,
      customer_satisfaction: {
        rating: 0,
        feedback: '',
        would_recommend: false
      },
      checkout_notes: '',
      assigned_staff: 'John Smith',
      created_at: '2024-01-20T17:30:00Z',
      updated_at: '2024-01-20T17:30:00Z'
    },
    {
      id: '2',
      reservation_id: '2',
      reservation_number: 'RES-2024-002',
      customer_id: '2',
      customer_name: 'Sarah Johnson',
      customer_email: '<EMAIL>',
      customer_phone: '+1555987654',
      vehicle_id: '2',
      vehicle_name: 'BMW X5 2022',
      vehicle_make: 'BMW',
      vehicle_model: 'X5',
      vehicle_year: 2022,
      plate_number: 'XYZ-456',
      vin_number: 'WBAFR7C50BC123456',
      pickup_date: '2024-01-10T10:00:00Z',
      return_date: '2024-01-18T17:00:00Z',
      actual_return_date: '2024-01-20T14:30:00Z',
      pickup_location: 'Downtown Dubai',
      return_location: 'Downtown Dubai',
      status: 'overdue',
      checkout_status: 'inspection_started',
      total_amount: 1050,
      deposit_amount: 1000,
      deposit_status: 'held',
      rental_days: 8,
      actual_days: 10,
      daily_rate: 130,
      vehicle_condition_pickup: {
        fuel_level: 100,
        mileage: 28000,
        exterior_condition: 'good',
        interior_condition: 'excellent',
        cleanliness_score: 90
      },
      vehicle_condition_return: {
        fuel_level: 45,
        mileage: 28750,
        exterior_damage: ['minor_scratch_rear_bumper', 'small_dent_door'],
        interior_condition: 'fair',
        cleanliness_score: 70,
        damage_photos: ['damage_1.jpg', 'damage_2.jpg'],
        damage_cost: 350
      },
      fuel_policy: 'full_to_full',
      fuel_charges: 85,
      mileage_limit: 1600,
      mileage_overage: 150,
      mileage_charges: 75,
      late_return_hours: 45,
      late_return_charges: 260,
      cleaning_charges: 50,
      damage_charges: 350,
      additional_charges: 0,
      total_additional_charges: 820,
      final_amount: 820,
      customer_satisfaction: {
        rating: 0,
        feedback: '',
        would_recommend: false
      },
      checkout_notes: 'Customer returned vehicle late with damage',
      checkout_start_time: '2024-01-20T14:30:00Z',
      assigned_staff: 'Maria Garcia',
      created_at: '2024-01-20T14:30:00Z',
      updated_at: '2024-01-20T15:00:00Z'
    }
  ]

  // Filter and search logic
  const filteredRentals = checkOutRentals.filter(rental => {
    const matchesSearch = !searchQuery ||
      rental.customer_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rental.reservation_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rental.plate_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rental.vehicle_name.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesStatus = !statusFilter || rental.status === statusFilter
    const matchesLocation = !locationFilter ||
      rental.return_location.toLowerCase().includes(locationFilter.toLowerCase())

    return matchesSearch && matchesStatus && matchesLocation
  })

  // Pagination
  const totalItems = filteredRentals.length
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedRentals = filteredRentals.slice(startIndex, startIndex + itemsPerPage)

  // Statistics
  const dueToday = checkOutRentals.filter(r => r.status === 'due_today').length
  const overdue = checkOutRentals.filter(r => r.status === 'overdue').length
  const inProgress = checkOutRentals.filter(r => ['inspecting', 'billing'].includes(r.status)).length
  const completedToday = checkOutRentals.filter(r =>
    r.status === 'completed' &&
    new Date(r.checkout_completion_time || '').toDateString() === new Date().toDateString()
  ).length

  const stats = [
    {
      label: t('dueToday'),
      value: dueToday.toString(),
      color: 'orange',
      icon: IconClock
    },
    {
      label: t('overdue'),
      value: overdue.toString(),
      color: 'red',
      icon: IconAlertTriangle
    },
    {
      label: t('inProgress'),
      value: inProgress.toString(),
      color: 'blue',
      icon: IconClipboardCheck
    },
    {
      label: t('completedToday'),
      value: completedToday.toString(),
      color: 'green',
      icon: IconCheck
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'due_today': return 'orange'
      case 'overdue': return 'red'
      case 'returned': return 'blue'
      case 'inspecting': return 'yellow'
      case 'billing': return 'purple'
      case 'completed': return 'green'
      default: return 'gray'
    }
  }

  const getCheckOutStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'gray'
      case 'inspection_started': return 'blue'
      case 'damage_assessed': return 'orange'
      case 'billing_calculated': return 'purple'
      case 'payment_processed': return 'cyan'
      case 'completed': return 'green'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const getCheckOutProgress = (status: string) => {
    switch (status) {
      case 'pending': return 0
      case 'inspection_started': return 20
      case 'damage_assessed': return 40
      case 'billing_calculated': return 60
      case 'payment_processed': return 80
      case 'completed': return 100
      default: return 0
    }
  }

  if (loading) {
    return <LoadingState />
  }

  return (
    <ErrorBoundary>
      <Container size="xl" py="md">
        <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
          <Group justify="space-between" mb="lg">
            <div>
              <Title order={2}>{t('checkOutManagement')}</Title>
              <Text c="dimmed" size="sm">{t('manageVehicleReturnAndCheckOutProcess')}</Text>
            </div>
            <Group>
              <Button variant="light" leftSection={<IconRefresh size={16} />}>
                {t('refresh')}
              </Button>
              <Button variant="light" leftSection={<IconDownload size={16} />}>
                {t('export')}
              </Button>
            </Group>
          </Group>

          <Tabs.List>
            <Tabs.Tab value="all-checkouts">{t('allCheckOuts')}</Tabs.Tab>
            <Tabs.Tab value="due-today">{t('dueToday')}</Tabs.Tab>
            <Tabs.Tab value="overdue">{t('overdue')}</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="all-checkouts">
            {/* Statistics Cards */}
            <Grid mb="lg">
              {stats.map((stat, index) => (
                <Grid.Col key={index} span={{ base: 12, sm: 6, md: 3 }}>
                  <Card withBorder h={80} p="md">
                    <Group justify="space-between" h="100%">
                      <div>
                        <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                          {stat.label}
                        </Text>
                        <Text fw={700} size="xl">
                          {stat.value}
                        </Text>
                      </div>
                      <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                    </Group>
                  </Card>
                </Grid.Col>
              ))}
            </Grid>

            {/* Search and Filters */}
            <Card withBorder mb="lg">
              <Grid>
                <Grid.Col span={{ base: 12, md: 4 }}>
                  <TextInput
                    placeholder={t('searchCheckOuts')}
                    leftSection={<IconSearch size={16} />}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 2 }}>
                  <Select
                    placeholder={t('status')}
                    leftSection={<IconFilter size={16} />}
                    value={statusFilter}
                    onChange={(value) => setStatusFilter(value || '')}
                    data={[
                      { value: '', label: t('allStatuses') },
                      { value: 'due_today', label: t('dueToday') },
                      { value: 'overdue', label: t('overdue') },
                      { value: 'returned', label: t('returned') },
                      { value: 'completed', label: t('completed') }
                    ]}
                    clearable
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <TextInput
                    placeholder={t('location')}
                    leftSection={<IconMapPin size={16} />}
                    value={locationFilter}
                    onChange={(e) => setLocationFilter(e.target.value)}
                  />
                </Grid.Col>
                <Grid.Col span={{ base: 12, md: 3 }}>
                  <Button
                    variant="light"
                    fullWidth
                    onClick={() => {
                      setSearchQuery('')
                      setStatusFilter('')
                      setLocationFilter('')
                    }}
                  >
                    {t('clearFilters')}
                  </Button>
                </Grid.Col>
              </Grid>
            </Card>

            {/* Check-Out Table */}
            <Card withBorder>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>{t('customer')}</Table.Th>
                    <Table.Th>{t('vehicle')}</Table.Th>
                    <Table.Th>{t('returnDate')}</Table.Th>
                    <Table.Th>{t('status')}</Table.Th>
                    <Table.Th>{t('progress')}</Table.Th>
                    <Table.Th>{t('additionalCharges')}</Table.Th>
                    <Table.Th>{t('staff')}</Table.Th>
                    <Table.Th>{t('actions')}</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {paginatedRentals.map((rental) => (
                    <Table.Tr key={rental.id}>
                      <Table.Td>
                        <Group gap="sm">
                          <Avatar size="sm" color={getStatusColor(rental.status)}>
                            {getInitials(rental.customer_name)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{rental.customer_name}</Text>
                            <Text size="xs" c="dimmed">{rental.reservation_number}</Text>
                          </div>
                        </Group>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text fw={500} size="sm">{rental.vehicle_name}</Text>
                          <Text size="xs" c="dimmed">{rental.plate_number}</Text>
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text size="sm">
                            {new Date(rental.return_date).toLocaleDateString()}
                          </Text>
                          {rental.actual_return_date && (
                            <Text size="xs" c={rental.late_return_hours > 0 ? 'red' : 'green'}>
                              {t('actual')}: {new Date(rental.actual_return_date).toLocaleDateString()}
                            </Text>
                          )}
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Badge color={getStatusColor(rental.status)} size="sm">
                          {t(rental.status)}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Group gap="xs" mb={4}>
                            <Text size="xs" c="dimmed">{t(rental.checkout_status)}</Text>
                            <Text size="xs" fw={500}>
                              {getCheckOutProgress(rental.checkout_status)}%
                            </Text>
                          </Group>
                          <Progress
                            value={getCheckOutProgress(rental.checkout_status)}
                            size="sm"
                            color={getCheckOutStatusColor(rental.checkout_status)}
                          />
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <div>
                          <Text fw={700} size="sm" c={rental.total_additional_charges > 0 ? 'red' : 'green'}>
                            ${rental.total_additional_charges}
                          </Text>
                          {rental.late_return_hours > 0 && (
                            <Text size="xs" c="red">
                              {rental.late_return_hours}h {t('late')}
                            </Text>
                          )}
                        </div>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{rental.assigned_staff}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <ActionIcon
                            variant="light"
                            size="sm"
                            onClick={() => {
                              setSelectedRental(rental)
                              setViewModalOpen(true)
                            }}
                          >
                            <IconEye size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="blue"
                            onClick={() => {
                              setSelectedRental(rental)
                              setCheckOutModalOpen(true)
                            }}
                          >
                            <IconClipboardCheck size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="orange"
                            onClick={() => {
                              setSelectedRental(rental)
                              setDamageModalOpen(true)
                            }}
                          >
                            <IconCamera size={16} />
                          </ActionIcon>
                          <ActionIcon
                            variant="light"
                            size="sm"
                            color="green"
                            onClick={() => {
                              setSelectedRental(rental)
                              setBillingModalOpen(true)
                            }}
                          >
                            <IconCalculator size={16} />
                          </ActionIcon>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>

              {/* Pagination */}
              <Group justify="space-between" mt="md">
                <Text size="sm" c="dimmed">
                  {t('showing')} {Math.min(startIndex + 1, totalItems)} - {Math.min(startIndex + itemsPerPage, totalItems)} {t('of')} {totalItems} {t('checkOuts')}
                </Text>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={totalPages}
                  size="sm"
                />
              </Group>
            </Card>
          </Tabs.Panel>

          <Tabs.Panel value="due-today">
            <Grid>
              {checkOutRentals
                .filter(r => r.status === 'due_today')
                .map((rental) => (
                  <Grid.Col key={rental.id} span={{ base: 12, md: 6 }}>
                    <Card withBorder>
                      <Group justify="space-between" mb="md">
                        <Group>
                          <Avatar color={getStatusColor(rental.status)}>
                            {getInitials(rental.customer_name)}
                          </Avatar>
                          <div>
                            <Text fw={500}>{rental.customer_name}</Text>
                            <Text size="sm" c="dimmed">{rental.vehicle_name}</Text>
                          </div>
                        </Group>
                        <Badge color={getStatusColor(rental.status)}>
                          {t(rental.status)}
                        </Badge>
                      </Group>

                      <Text size="sm" c="dimmed">{t('expectedReturn')}</Text>
                      <Text fw={500}>{new Date(rental.return_date).toLocaleString()}</Text>
                      <Text size="xs" c="dimmed" mt="xs">{rental.return_location}</Text>

                      <Group justify="flex-end" mt="md">
                        <Button
                          size="xs"
                          variant="light"
                          onClick={() => {
                            setSelectedRental(rental)
                            setCheckOutModalOpen(true)
                          }}
                        >
                          {t('startCheckOut')}
                        </Button>
                      </Group>
                    </Card>
                  </Grid.Col>
                ))}
            </Grid>
          </Tabs.Panel>

          <Tabs.Panel value="overdue">
            <Grid>
              {checkOutRentals
                .filter(r => r.status === 'overdue')
                .map((rental) => (
                  <Grid.Col key={rental.id} span={{ base: 12, md: 6 }}>
                    <Card withBorder>
                      <Alert color="red" icon={<IconAlertTriangle size={16} />} mb="md">
                        <Text fw={500}>{t('overdueReturn')}</Text>
                        <Text size="sm">
                          {rental.late_return_hours} {t('hoursLate')} - ${rental.late_return_charges} {t('charges')}
                        </Text>
                      </Alert>

                      <Group justify="space-between" mb="md">
                        <Group>
                          <Avatar color="red">
                            {getInitials(rental.customer_name)}
                          </Avatar>
                          <div>
                            <Text fw={500}>{rental.customer_name}</Text>
                            <Text size="sm" c="dimmed">{rental.vehicle_name}</Text>
                          </div>
                        </Group>
                        <Badge color="red">
                          {t('overdue')}
                        </Badge>
                      </Group>

                      <Group justify="flex-end" mt="md">
                        <Button
                          size="xs"
                          variant="light"
                          color="red"
                          onClick={() => {
                            setSelectedRental(rental)
                            setCheckOutModalOpen(true)
                          }}
                        >
                          {t('processReturn')}
                        </Button>
                      </Group>
                    </Card>
                  </Grid.Col>
                ))}
            </Grid>
          </Tabs.Panel>
        </Tabs>

        {/* View Check-Out Modal */}
        <Modal
          opened={viewModalOpen}
          onClose={() => setViewModalOpen(false)}
          title={selectedRental ? selectedRental.reservation_number : t('checkOutDetails')}
          size="xl"
        >
          {selectedRental && (
            <Stack>
              <Grid>
                <Grid.Col span={8}>
                  <Grid>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('customer')}</Text>
                      <Text fw={500}>{selectedRental.customer_name}</Text>
                      <Text size="xs" c="dimmed">{selectedRental.customer_email}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('vehicle')}</Text>
                      <Text fw={500}>{selectedRental.vehicle_name}</Text>
                      <Text size="xs" c="dimmed">{selectedRental.plate_number}</Text>
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('returnDate')}</Text>
                      <Text fw={500}>{new Date(selectedRental.return_date).toLocaleDateString()}</Text>
                      {selectedRental.actual_return_date && (
                        <Text size="xs" c={selectedRental.late_return_hours > 0 ? 'red' : 'green'}>
                          {t('actualReturn')}: {new Date(selectedRental.actual_return_date).toLocaleDateString()}
                        </Text>
                      )}
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <Text size="sm" c="dimmed">{t('status')}</Text>
                      <Badge color={getStatusColor(selectedRental.status)}>
                        {t(selectedRental.status)}
                      </Badge>
                    </Grid.Col>
                  </Grid>
                </Grid.Col>
                <Grid.Col span={4}>
                  <Card withBorder>
                    <Stack align="center">
                      <Avatar size="xl" color={getStatusColor(selectedRental.status)}>
                        {getInitials(selectedRental.customer_name)}
                      </Avatar>
                      <div style={{ textAlign: 'center' }}>
                        <Text fw={700} size="lg">{selectedRental.customer_name}</Text>
                        <Text size="sm" c="dimmed">{selectedRental.reservation_number}</Text>
                      </div>
                    </Stack>
                  </Card>
                </Grid.Col>
              </Grid>

              <Divider />

              <div>
                <Text size="sm" c="dimmed" mb="xs">{t('checkOutProgress')}</Text>
                <Progress
                  value={getCheckOutProgress(selectedRental.checkout_status)}
                  size="lg"
                  color={getCheckOutStatusColor(selectedRental.checkout_status)}
                />
                <Text size="xs" c="dimmed" mt="xs">
                  {t('currentStep')}: {t(selectedRental.checkout_status)}
                </Text>
              </div>

              <Grid>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('fuelCharges')}</Text>
                  <Text fw={700} c="red">${selectedRental.fuel_charges}</Text>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('lateCharges')}</Text>
                  <Text fw={700} c="red">${selectedRental.late_return_charges}</Text>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('damageCharges')}</Text>
                  <Text fw={700} c="red">${selectedRental.damage_charges}</Text>
                </Grid.Col>
                <Grid.Col span={3}>
                  <Text size="sm" c="dimmed">{t('totalAdditional')}</Text>
                  <Text fw={700} size="xl" c="red">${selectedRental.total_additional_charges}</Text>
                </Grid.Col>
              </Grid>

              {selectedRental.checkout_notes && (
                <>
                  <Divider />
                  <div>
                    <Text size="sm" c="dimmed">{t('checkOutNotes')}</Text>
                    <Text>{selectedRental.checkout_notes}</Text>
                  </div>
                </>
              )}
            </Stack>
          )}
        </Modal>

        {/* Check-Out Process Modal */}
        <Modal
          opened={checkOutModalOpen}
          onClose={() => setCheckOutModalOpen(false)}
          title={t('checkOutProcess')}
          size="xl"
        >
          {selectedRental && (
            <Stack>
              <Group justify="space-between">
                <div>
                  <Text fw={500} size="lg">{selectedRental.customer_name}</Text>
                  <Text size="sm" c="dimmed">{selectedRental.vehicle_name}</Text>
                </div>
                <Badge color={getStatusColor(selectedRental.status)}>
                  {t(selectedRental.status)}
                </Badge>
              </Group>

              <Stepper active={checkOutStep} onStepClick={setCheckOutStep} allowNextStepsSelect={false}>
                <Stepper.Step
                  label={t('inspection')}
                  description={t('inspectVehicleCondition')}
                  icon={<IconClipboardCheck size={16} />}
                >
                  <Stack>
                    <Text fw={500}>{t('vehicleReturnInspection')}</Text>
                    <Grid>
                      <Grid.Col span={6}>
                        <NumberInput
                          label={t('currentFuelLevel')}
                          placeholder="0"
                          min={0}
                          max={100}
                          suffix="%"
                          defaultValue={selectedRental.vehicle_condition_return.fuel_level}
                        />
                        <Text size="xs" c="dimmed">
                          {t('pickupLevel')}: {selectedRental.vehicle_condition_pickup.fuel_level}%
                        </Text>
                      </Grid.Col>
                      <Grid.Col span={6}>
                        <NumberInput
                          label={t('currentMileage')}
                          placeholder="0"
                          min={0}
                          defaultValue={selectedRental.vehicle_condition_return.mileage}
                        />
                        <Text size="xs" c="dimmed">
                          {t('pickupMileage')}: {selectedRental.vehicle_condition_pickup.mileage.toLocaleString()}
                        </Text>
                      </Grid.Col>
                      <Grid.Col span={6}>
                        <NumberInput
                          label={t('cleanlinessScore')}
                          placeholder="0"
                          min={0}
                          max={100}
                          suffix="/100"
                          defaultValue={selectedRental.vehicle_condition_return.cleanliness_score}
                        />
                      </Grid.Col>
                      <Grid.Col span={6}>
                        <Select
                          label={t('interiorCondition')}
                          placeholder={t('selectCondition')}
                          data={[
                            { value: 'excellent', label: t('excellent') },
                            { value: 'good', label: t('good') },
                            { value: 'fair', label: t('fair') },
                            { value: 'poor', label: t('poor') }
                          ]}
                          defaultValue={selectedRental.vehicle_condition_return.interior_condition}
                        />
                      </Grid.Col>
                      <Grid.Col span={12}>
                        <Textarea
                          label={t('damageNotes')}
                          placeholder={t('describeDamageOrIssues')}
                          rows={3}
                        />
                      </Grid.Col>
                      <Grid.Col span={12}>
                        <FileInput
                          label={t('damagePhotos')}
                          placeholder={t('uploadDamagePhotos')}
                          multiple
                          accept="image/*"
                        />
                      </Grid.Col>
                    </Grid>
                  </Stack>
                </Stepper.Step>

                <Stepper.Step
                  label={t('billing')}
                  description={t('calculateAdditionalCharges')}
                  icon={<IconCalculator size={16} />}
                >
                  <Stack>
                    <Text fw={500}>{t('additionalChargesCalculation')}</Text>
                    <Grid>
                      <Grid.Col span={6}>
                        <NumberInput
                          label={t('fuelCharges')}
                          placeholder="0"
                          min={0}
                          prefix="$"
                          defaultValue={selectedRental.fuel_charges}
                        />
                      </Grid.Col>
                      <Grid.Col span={6}>
                        <NumberInput
                          label={t('cleaningCharges')}
                          placeholder="0"
                          min={0}
                          prefix="$"
                          defaultValue={selectedRental.cleaning_charges}
                        />
                      </Grid.Col>
                      <Grid.Col span={6}>
                        <NumberInput
                          label={t('mileageCharges')}
                          placeholder="0"
                          min={0}
                          prefix="$"
                          defaultValue={selectedRental.mileage_charges}
                        />
                      </Grid.Col>
                      <Grid.Col span={6}>
                        <NumberInput
                          label={t('lateReturnCharges')}
                          placeholder="0"
                          min={0}
                          prefix="$"
                          defaultValue={selectedRental.late_return_charges}
                        />
                      </Grid.Col>
                      <Grid.Col span={6}>
                        <NumberInput
                          label={t('damageCharges')}
                          placeholder="0"
                          min={0}
                          prefix="$"
                          defaultValue={selectedRental.damage_charges}
                        />
                      </Grid.Col>
                      <Grid.Col span={6}>
                        <NumberInput
                          label={t('otherCharges')}
                          placeholder="0"
                          min={0}
                          prefix="$"
                          defaultValue={selectedRental.additional_charges}
                        />
                      </Grid.Col>
                      <Grid.Col span={12}>
                        <Divider />
                        <Group justify="space-between">
                          <Text fw={500}>{t('totalAdditionalCharges')}</Text>
                          <Text fw={700} size="xl" c="red">
                            ${selectedRental.total_additional_charges}
                          </Text>
                        </Group>
                      </Grid.Col>
                    </Grid>
                  </Stack>
                </Stepper.Step>

                <Stepper.Step
                  label={t('payment')}
                  description={t('processPaymentAndDeposit')}
                  icon={<IconCreditCard size={16} />}
                >
                  <Stack>
                    <Text fw={500}>{t('paymentProcessing')}</Text>
                    <Alert color="blue" icon={<IconCreditCard size={16} />}>
                      <Text fw={500}>{t('depositAndCharges')}</Text>
                      <Text size="sm">
                        {t('deposit')}: ${selectedRental.deposit_amount} | {t('charges')}: ${selectedRental.total_additional_charges}
                      </Text>
                    </Alert>
                    <Grid>
                      <Grid.Col span={6}>
                        <Select
                          label={t('paymentMethod')}
                          placeholder={t('selectPaymentMethod')}
                          data={[
                            { value: 'deposit_deduction', label: t('deductFromDeposit') },
                            { value: 'credit_card', label: t('creditCard') },
                            { value: 'cash', label: t('cash') },
                            { value: 'bank_transfer', label: t('bankTransfer') }
                          ]}
                        />
                      </Grid.Col>
                      <Grid.Col span={6}>
                        <NumberInput
                          label={t('depositRelease')}
                          placeholder="0"
                          min={0}
                          prefix="$"
                          defaultValue={selectedRental.deposit_amount - selectedRental.total_additional_charges}
                        />
                      </Grid.Col>
                    </Grid>
                  </Stack>
                </Stepper.Step>

                <Stepper.Step
                  label={t('feedback')}
                  description={t('collectCustomerFeedback')}
                  icon={<IconStar size={16} />}
                >
                  <Stack>
                    <Text fw={500}>{t('customerSatisfactionSurvey')}</Text>
                    <div>
                      <Text size="sm" mb="xs">{t('overallRating')}</Text>
                      <Rating size="lg" />
                    </div>
                    <Textarea
                      label={t('feedback')}
                      placeholder={t('customerFeedbackComments')}
                      rows={4}
                    />
                    <Checkbox label={t('wouldRecommendToFriends')} />
                    <Textarea
                      label={t('finalNotes')}
                      placeholder={t('anyFinalNotesOrObservations')}
                      rows={2}
                    />
                  </Stack>
                </Stepper.Step>
              </Stepper>

              <Group justify="space-between" mt="xl">
                <Button variant="light" onClick={() => setCheckOutModalOpen(false)}>
                  {t('cancel')}
                </Button>
                <Group>
                  {checkOutStep > 0 && (
                    <Button variant="light" onClick={() => setCheckOutStep(checkOutStep - 1)}>
                      {t('previous')}
                    </Button>
                  )}
                  {checkOutStep < 3 ? (
                    <Button onClick={() => setCheckOutStep(checkOutStep + 1)}>
                      {t('next')}
                    </Button>
                  ) : (
                    <Button color="green" leftSection={<IconCheck size={16} />}>
                      {t('completeCheckOut')}
                    </Button>
                  )}
                </Group>
              </Group>
            </Stack>
          )}
        </Modal>
      </Container>
    </ErrorBoundary>
  )
}

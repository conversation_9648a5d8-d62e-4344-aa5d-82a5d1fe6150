import { useState } from 'react'
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Card,
  Table,
  Badge,
  ActionIcon,
  TextInput,
  Select,
  Grid,
  Paper,
  Stack,
  Modal,
  Divider,
  Alert,
  Tabs,
  Avatar
} from '@mantine/core'
import {
  IconAlertTriangle,
  IconCamera,
  IconCar,
  IconCheck,
  IconClock,
  IconDownload,
  IconEdit,
  IconEye,
  IconPlus,
  IconSearch
} from '@tabler/icons-react'
import { useTranslation } from 'react-i18next'

interface DamageReport {
  id: string
  reservationNumber: string
  customerName: string
  customerPhone: string
  vehicleName: string
  plateNumber: string
  inspectionDate: string
  inspectionType: 'check-in' | 'check-out' | 'maintenance'
  status: 'pending' | 'in-progress' | 'completed' | 'damage-found'
  location: string
  damageCount: number
  severity: 'none' | 'minor' | 'major' | 'severe'
}

export function DamageInspectionWorking() {
  const { t } = useTranslation()
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [modalOpen, setModalOpen] = useState(false)

  // Simple mock data - same pattern as other working pages
  const inspections: DamageReport[] = [
    {
      id: '1',
      reservationNumber: 'RES-2024-001',
      customerName: 'Ahmed Al-Rashid',
      customerPhone: '+971-50-123-4567',
      vehicleName: 'Toyota Camry 2023',
      plateNumber: 'ABC-123',
      inspectionDate: '2024-01-20',
      inspectionType: 'check-out',
      status: 'damage-found',
      location: 'Dubai Airport',
      damageCount: 2,
      severity: 'minor'
    },
    {
      id: '2',
      reservationNumber: 'RES-2024-002',
      customerName: 'Sarah Johnson',
      customerPhone: '******-987-6543',
      vehicleName: 'BMW X5 2022',
      plateNumber: 'XYZ-456',
      inspectionDate: '2024-01-20',
      inspectionType: 'check-in',
      status: 'pending',
      location: 'Abu Dhabi',
      damageCount: 0,
      severity: 'none'
    },
    {
      id: '3',
      reservationNumber: 'RES-2024-003',
      customerName: 'Mohammed Hassan',
      customerPhone: '+971-55-789-0123',
      vehicleName: 'Mercedes C-Class 2023',
      plateNumber: 'DEF-789',
      inspectionDate: '2024-01-20',
      inspectionType: 'maintenance',
      status: 'completed',
      location: 'Sharjah',
      damageCount: 0,
      severity: 'none'
    }
  ]

  const stats = [
    { label: t('pendingInspections'), value: '5', color: 'blue', icon: IconClock },
    { label: t('damageFound'), value: '3', color: 'red', icon: IconAlertTriangle },
    { label: t('completedToday'), value: '12', color: 'green', icon: IconCheck },
    { label: t('totalInspections'), value: '45', color: 'gray', icon: IconCar}
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'blue'
      case 'in-progress': return 'orange'
      case 'completed': return 'green'
      case 'damage-found': return 'red'
      default: return 'gray'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'none': return 'green'
      case 'minor': return 'yellow'
      case 'major': return 'orange'
      case 'severe': return 'red'
      default: return 'gray'
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const filteredInspections = inspections.filter(inspection => {
    const matchesSearch = inspection.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         inspection.reservationNumber.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = !statusFilter || inspection.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  return (
    <Container size="xl" py="md">
      {/* Header - same pattern as other pages */}
      <Group justify="space-between" mb="lg">
        <div>
          <Title order={2}>{t('damageInspection')}</Title>
          <Text c="dimmed" size="sm">{t('manageVehicleConditionInspections')}</Text>
        </div>
        <Group>
          <Button variant="light" leftSection={<IconDownload size={16} />}>
            {t('exportReport')}
          </Button>
          <Button leftSection={<IconPlus size={16} />} onClick={() => setModalOpen(true)}>
            {t('newInspection')}
          </Button>
        </Group>
      </Group>

      <Tabs value={activeTab} onChange={(value) => value && setActiveTab(value)}>
        <Tabs.List>
          <Tabs.Tab value="overview">{t('overview')}</Tabs.Tab>
          <Tabs.Tab value="inspections">{t('allInspections')}</Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="overview" pt="md">
          {/* Stats - same pattern as other pages */}
          <Grid mb="lg">
            {stats.map((stat) => (
              <Grid.Col key={stat.label} span={{ base: 12, sm: 6, md: 3 }}>
                <Paper p="md" withBorder>
                  <Group justify="space-between">
                    <div>
                      <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                        {stat.label}
                      </Text>
                      <Text fw={700} size="xl">
                        {stat.value}
                      </Text>
                    </div>
                    <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
                  </Group>
                </Paper>
              </Grid.Col>
            ))}
          </Grid>

          <Grid>
            {/* Alerts - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('damageAlerts')}</Title>
                
                <Stack gap="sm">
                  <Alert icon={<IconAlertTriangle size={16} />} color="red">
                    <Text fw={500} size="sm">{t('severeDamageFound')}</Text>
                    <Text size="xs">1 {t('vehicleWithSevereDamage')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconClock size={16} />} color="orange">
                    <Text fw={500} size="sm">{t('pendingInspections')}</Text>
                    <Text size="xs">5 {t('vehiclesAwaitingInspection')}</Text>
                  </Alert>
                  
                  <Alert icon={<IconCamera size={16} />} color="blue">
                    <Text fw={500} size="sm">{t('photoDocumentation')}</Text>
                    <Text size="xs">3 {t('inspectionsNeedPhotos')}</Text>
                  </Alert>
                </Stack>
              </Card>
            </Grid.Col>

            {/* Recent Inspections - same pattern as other pages */}
            <Grid.Col span={{ base: 12, md: 6 }}>
              <Card withBorder>
                <Title order={4} mb="md">{t('recentInspections')}</Title>
                
                <Stack gap="sm">
                  {inspections.slice(0, 3).map((inspection) => (
                    <Paper key={inspection.id} p="sm" withBorder>
                      <Group justify="space-between">
                        <Group>
                          <Avatar color={getStatusColor(inspection.status)} radius="xl" size="sm">
                            {getInitials(inspection.customerName)}
                          </Avatar>
                          <div>
                            <Text fw={500} size="sm">{inspection.customerName}</Text>
                            <Text size="xs" c="dimmed">{inspection.vehicleName} - {inspection.inspectionType}</Text>
                          </div>
                        </Group>
                        <div style={{ textAlign: 'right' }}>
                          <Badge color={getSeverityColor(inspection.severity)} size="sm">
                            {inspection.damageCount > 0 ? `${inspection.damageCount} damages` : 'No damage'}
                          </Badge>
                          <Text size="xs" c="dimmed">{inspection.location}</Text>
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Card>
            </Grid.Col>
          </Grid>
        </Tabs.Panel>

        <Tabs.Panel value="inspections" pt="md">
          {/* Filters - same pattern as other pages */}
          <Card withBorder mb="lg">
            <Grid>
              <Grid.Col span={{ base: 12, md: 4 }}>
                <TextInput
                  placeholder={t('searchInspections')}
                  leftSection={<IconSearch size={16} />}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Select
                  placeholder={t('allStatus')}
                  data={[
                    { value: '', label: t('allStatus') },
                    { value: 'pending', label: t('pending') },
                    { value: 'in-progress', label: t('in') },
                    { value: 'completed', label: t('completed') },
                    { value: 'damage-found', label: t('damageFound') }
                  ]}
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value || '')}
                />
              </Grid.Col>
              <Grid.Col span={{ base: 12, md: 3 }}>
                <Button variant="light" fullWidth leftSection={<IconDownload size={14} />}>
                  {t('export')}
                </Button>
              </Grid.Col>
            </Grid>
          </Card>

          {/* Table - same pattern as other pages */}
          <Card withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>{t('customer')}</Table.Th>
                  <Table.Th>{t('vehicle')}</Table.Th>
                  <Table.Th>{t('inspectionType')}</Table.Th>
                  <Table.Th>{t('status')}</Table.Th>
                  <Table.Th>{t('damages')}</Table.Th>
                  <Table.Th>{t('severity')}</Table.Th>
                  <Table.Th>{t('actions')}</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredInspections.map((inspection) => (
                  <Table.Tr key={inspection.id}>
                    <Table.Td>
                      <Group>
                        <Avatar color={getStatusColor(inspection.status)} radius="xl" size="sm">
                          {getInitials(inspection.customerName)}
                        </Avatar>
                        <div>
                          <Text fw={500} size="sm">{inspection.customerName}</Text>
                          <Text size="xs" c="dimmed">{inspection.customerPhone}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <div>
                        <Text fw={500} size="sm">{inspection.vehicleName}</Text>
                        <Text size="xs" c="dimmed">{inspection.plateNumber}</Text>
                      </div>
                    </Table.Td>
                    <Table.Td>
                      <Badge variant="light">
                        {t(inspection.inspectionType)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getStatusColor(inspection.status)}>
                        {t(inspection.status)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text fw={500} size="sm">{inspection.damageCount}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={getSeverityColor(inspection.severity)} variant="light">
                        {t(inspection.severity)}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <ActionIcon variant="light" size="sm">
                          <IconEye size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconCamera size={14} />
                        </ActionIcon>
                        <ActionIcon variant="light" size="sm">
                          <IconEdit size={14} />
                        </ActionIcon>
                        <Button size="xs" leftSection={<IconCheck size={12} />}>
                          {t('inspect')}
                        </Button>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card>
        </Tabs.Panel>
      </Tabs>

      {/* Simple Modal - same pattern as other pages */}
      <Modal
        opened={modalOpen}
        onClose={() => setModalOpen(false)}
        title={t('newDamageInspection')}
        size="md"
      >
        <Stack>
          <Select
            label={t('vehicle')}
            placeholder={t('selectVehicle')}
            data={[
              { value: '1', label: 'Toyota Camry 2023 - ABC-123' },
              { value: '2', label: 'BMW X5 2022 - XYZ-456' },
              { value: '3', label: 'Mercedes C-Class 2023 - DEF-789' }
            ]}
            required
          />

          <Select
            label={t('inspectionType')}
            placeholder={t('selectInspectionType')}
            data={[
              { value: 'check-in', label: t('checkIn') },
              { value: 'check-out', label: t('checkOut') },
              { value: 'maintenance', label: t('maintenance') }
            ]}
            required
          />

          <TextInput
            label={t('notes')}
            placeholder={t('enterInitialNotes')}
          />

          <Divider />

          <Group justify="flex-end">
            <Button variant="light" onClick={() => setModalOpen(false)}>
              {t('cancel')}
            </Button>
            <Button leftSection={<IconCamera size={16} />} onClick={() => setModalOpen(false)}>
              {t('startInspection')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  )
}
